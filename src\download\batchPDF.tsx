import { createSignal, For, Show, onMount, onCleanup } from "solid-js";

interface CSVRow {
  DcArticleId: string;
  Journal: string;
  PDFLink: string;
  PDFOrgFilename: string;
  DOILink: string;
  IssueYear: string;
}

interface DownloadLog {
  filename: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
}

interface DownloadProgress {
  bytesReceived: number;
  totalBytes: number;
  filename: string;
}

declare global {
  interface Window {
    showDirectoryPicker(): Promise<FileSystemDirectoryHandle>;
    showOpenFilePicker(options?: {
      types?: Array<{
        description: string;
        accept: Record<string, string[]>;
      }>;
      excludeAcceptAllOption?: boolean;
      multiple?: boolean;
    }): Promise<FileSystemFileHandle[]>;
  }
}

const PdfDownload = () => {
  const [csvContent, setCsvContent] = createSignal<CSVRow[]>([]);
  const [isDownloading, setIsDownloading] = createSignal(false);
  const [isPaused, setIsPaused] = createSignal(false);
  const [downloadLogs, setDownloadLogs] = createSignal<DownloadLog[]>([]);
  const [progress, setProgress] = createSignal(0);
  const [outputDir, setOutputDir] = createSignal('');
  const [dirHandle, setDirHandle] = createSignal<FileSystemDirectoryHandle | null>(null);
  const [currentDownload, setCurrentDownload] = createSignal<DownloadProgress | null>(null);

  const selectOutputDir = async () => {
    try {
      const handle = await window.showDirectoryPicker();
      setDirHandle(handle);
      setOutputDir(handle.name);
    } catch (err) {
      console.error('目录选择失败:', err);
    }
  };

  // 使用 Chrome 文件系统 API 处理文件选择
  const handleFileSelect = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const rows = text.split('\n')
        .filter(row => row.trim())
        .map(row => {
          const values = row.split(',').map(v => v.trim().replace(/^"|"$/g, ''));
          return {
            DcArticleId: values[0],
            Journal: values[1],
            PDFLink: values[2],
            PDFOrgFilename: values[3],
            DOILink: values[4],
            IssueYear: values[5]
          };
        });
      
      rows.shift(); // 移除表头
      setCsvContent(rows);
      setDownloadLogs(rows.map(row => ({
        filename: row.PDFOrgFilename,
        status: 'pending' as const
      })));
    };
    reader.readAsText(file);
  };

  const updateLogStatus = (filename: string, status: 'success' | 'error', message?: string) => {
    setDownloadLogs(prev => 
      prev.map(log => 
        log.filename === filename 
          ? { ...log, status, message } 
          : log
      )
    );
  };

  const saveFile = async (directoryHandle: FileSystemDirectoryHandle, filename: string, content: Blob) => {
    try {
      // 创建子目录结构
      const parts = filename.split('/');
      const actualFilename = parts.pop() || filename;
      let currentHandle = directoryHandle;
      
      for (const part of parts) {
        currentHandle = await currentHandle.getDirectoryHandle(part, { create: true });
      }
      
      setCurrentDownload({
        bytesReceived: 0,
        totalBytes: content.size,
        filename
      });

      // 创建并写入文件
      const fileHandle = await currentHandle.getFileHandle(actualFilename, { create: true });
      const writable = await fileHandle.createWritable();
      
      // 使用 ReadableStream 来跟踪进度
      const reader = new Response(content).body!.getReader();
      let receivedLength = 0;

      while(true) {
        const {done, value} = await reader.read();
        
        if (done) break;
        
        await writable.write(value);
        receivedLength += value.length;

        setCurrentDownload({
          bytesReceived: receivedLength,
          totalBytes: content.size,
          filename
        });
      }

      await writable.close();
      setCurrentDownload(null);
      return true;
    } catch (err) {
      console.error(`保存文件失败: ${filename}`, err);
      throw err;
    }
  };

  const handleDownload = async () => {
    if (!dirHandle()) {
      console.error('未选择输出目录');
      return;
    }

    setIsDownloading(true);
    setProgress(0);
    setIsPaused(false);
    
    const downloads = csvContent();
    const totalFiles = downloads.length;

    for (let i = 0; i < downloads.length; i++) {
      if (isPaused()) {
        break;
      }

      const item = downloads[i];
      try {
        const response = await fetch(item.PDFLink);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const blob = await response.blob();
        const filePath = `${item.PDFOrgFilename}`;
        
        await saveFile(dirHandle()!, filePath, blob);
        updateLogStatus(item.PDFOrgFilename, 'success');
      } catch (error) {
        console.error(`下载失败: ${item.PDFOrgFilename}`, error);
        updateLogStatus(item.PDFOrgFilename, 'error', error instanceof Error ? error.message : String(error));
      }

      setProgress(((i + 1) / totalFiles) * 100);
    }

    setIsDownloading(false);
    setIsPaused(false);
  };

  const handlePauseResume = () => {
    setIsPaused(!isPaused());
  };

  const handleStop = () => {
    setIsDownloading(false);
    setIsPaused(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-emerald-600';
      case 'error': return 'text-rose-600';
      default: return 'text-slate-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success': return '下载成功';
      case 'error': return '下载失败';
      default: return '等待下载';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
  };

  return (
    <div class="space-y-8">
      <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <h1 class="text-2xl font-bold text-slate-800 mb-6">批量下载PDF</h1>
        
        <div class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">CSV文件</label>
            <input
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              class="block w-full text-sm text-slate-500 
                file:mr-4 file:py-2 file:px-4 
                file:rounded-full file:border-0 
                file:text-sm file:font-semibold
                file:bg-blue-50 file:text-blue-700 
                hover:file:bg-blue-100
                cursor-pointer disabled:opacity-50"
              disabled={isDownloading()}
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">输出目录</label>
            <div class="flex gap-3">
              <input
                type="text"
                value={outputDir()}
                readonly
                class="block w-full rounded-lg border-slate-200 bg-slate-50 px-4 py-2.5 text-sm"
                placeholder="请选择输出目录"
              />
              <button
                onClick={selectOutputDir}
                disabled={isDownloading()}
                class="w-[8rem] px-4 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 disabled:opacity-50"
              >
                选择目录
              </button>
            </div>
          </div>
        </div>

        <div class="mt-8 space-y-4">
          <div class="relative">
            <button
              onClick={handleDownload}
              disabled={isDownloading() || csvContent().length === 0 || !outputDir()}
              class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium
                hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed
                transition-colors duration-200 relative overflow-hidden"
            >
              <div 
                class="absolute left-0 top-0 h-full bg-blue-800/30 transition-all duration-300"
                style={{ width: `${progress()}%` }}
              />
              <div class="relative z-10 flex items-center justify-center">
                {isDownloading() ? (
                  <>
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    下载中 ({Math.round(progress())}%)
                  </>
                ) : "开始下载"}
              </div>
            </button>
          </div>

          <Show when={isDownloading()}>
            <div class="flex gap-4">
              <button
                onClick={handlePauseResume}
                class="flex-1 px-6 py-2.5 rounded-lg font-medium border
                  transition-colors duration-200 hover:bg-slate-50 
                  flex items-center justify-center gap-2"
              >
                {isPaused() ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    </svg>
                    <span class="text-green-600">继续下载</span>
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m-9-3a9 9 0 1118 0 9 9 0 01-18 0z" />
                    </svg>
                    <span class="text-yellow-600">暂停下载</span>
                  </>
                )}
              </button>
              
              <button
                onClick={handleStop}
                class="flex-1 px-6 py-2.5 rounded-lg font-medium border
                  text-red-600 hover:bg-red-50
                  transition-colors duration-200
                  flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                </svg>
                停止下载
              </button>
            </div>
          </Show>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-200">
          <h2 class="font-medium text-slate-800">下载日志</h2>
        </div>
        <div class="divide-y max-h-[400px] overflow-auto">
          <For each={downloadLogs()}>
            {(log) => (
              <div class="px-6 py-3 flex justify-between items-center hover:bg-slate-50">
                <span class="text-sm text-slate-600">{log.filename}</span>
                <span class={`text-sm font-medium ${getStatusColor(log.status)}`}>
                  {getStatusText(log.status)}
                  {log.message && ` - ${log.message}`}
                </span>
              </div>
            )}
          </For>
        </div>
      </div>

      <Show when={currentDownload()}>
        <div class="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg border border-blue-200">
          <div class="text-sm text-slate-600">
            正在下载: {currentDownload()?.filename}
          </div>
          <div class="mt-2">
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                style={{ 
                  width: `${((currentDownload()?.bytesReceived ?? 0) / (currentDownload()?.totalBytes ?? 1)) * 100}%` 
                }}
              ></div>
            </div>
          </div>
          <div class="text-xs text-slate-500 mt-1">
            {formatBytes(currentDownload()?.bytesReceived || 0)} / {formatBytes(currentDownload()?.totalBytes || 0)}
          </div>
        </div>
      </Show>
    </div>
  );
};

export default PdfDownload;