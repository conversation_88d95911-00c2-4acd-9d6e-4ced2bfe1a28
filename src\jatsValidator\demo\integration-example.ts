// JATS XML校验器集成示例
// 展示如何在其他模块中使用校验器

import { jatsValidator, ValidationResult } from '../index';

/**
 * 在XML批量修改模块中集成校验器
 */
export class EnhancedXmlProcessor {
  
  /**
   * 处理XML文件前先进行校验
   */
  async processXmlWithValidation(xmlContent: string): Promise<{
    processed: boolean;
    validationResult: ValidationResult;
    processedContent?: string;
    errors?: string[];
  }> {
    
    // 1. 先校验XML
    console.log('开始校验XML文档...');
    const validationResult = jatsValidator.validate(xmlContent, {
      strictMode: false,
      checkWellFormedness: true,
      checkJATSCompliance: true,
      checkStructure: true,
      checkAttributes: true
    });
    
    // 2. 根据校验结果决定是否继续处理
    if (validationResult.errors.length > 0) {
      console.warn(`XML文档存在 ${validationResult.errors.length} 个错误`);
      
      // 如果有严重错误，停止处理
      const criticalErrors = validationResult.errors.filter(error => 
        ['XML_PARSE_ERROR', 'UNCLOSED_TAG', 'TAG_MISMATCH'].includes(error.code)
      );
      
      if (criticalErrors.length > 0) {
        return {
          processed: false,
          validationResult,
          errors: criticalErrors.map(err => err.message)
        };
      }
    }
    
    // 3. 如果只有警告或轻微错误，继续处理
    console.log('XML校验通过，开始处理...');
    
    try {
      // 这里是原有的XML处理逻辑
      const processedContent = this.performXmlProcessing(xmlContent);
      
      // 4. 处理完成后再次校验
      const finalValidation = jatsValidator.validate(processedContent);
      
      return {
        processed: true,
        validationResult: finalValidation,
        processedContent
      };
      
    } catch (error) {
      return {
        processed: false,
        validationResult,
        errors: [`处理过程中发生错误: ${error.message}`]
      };
    }
  }
  
  private performXmlProcessing(xmlContent: string): string {
    // 模拟XML处理逻辑
    return xmlContent.replace(/encoding="utf-8"/g, 'encoding="UTF-8"');
  }
}

/**
 * 在参考文献处理模块中集成校验器
 */
export class EnhancedReferenceProcessor {
  
  /**
   * 处理参考文献前校验XML结构
   */
  async processReferencesWithValidation(xmlContent: string): Promise<{
    valid: boolean;
    references: any[];
    validationSummary: any;
  }> {
    
    // 1. 快速检查XML是否有效
    const isValid = jatsValidator.isValidJATS(xmlContent);
    
    if (!isValid) {
      const summary = jatsValidator.getErrorSummary(xmlContent);
      console.warn('XML文档无效，但尝试继续处理参考文献');
      
      return {
        valid: false,
        references: [],
        validationSummary: summary
      };
    }
    
    // 2. 获取详细的校验报告
    const detailedReport = jatsValidator.getDetailedReport(xmlContent);
    
    // 3. 检查是否包含参考文献相关的错误
    const refErrors = detailedReport.errors.filter(error => 
      error.element?.includes('ref') || 
      error.message.includes('ref-list') ||
      error.message.includes('citation')
    );
    
    if (refErrors.length > 0) {
      console.warn('发现参考文献相关错误:', refErrors);
    }
    
    // 4. 继续处理参考文献
    const references = this.extractReferences(xmlContent);
    
    return {
      valid: true,
      references,
      validationSummary: detailedReport.summary
    };
  }
  
  private extractReferences(xmlContent: string): any[] {
    // 模拟参考文献提取逻辑
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
    const refs = xmlDoc.querySelectorAll('ref');
    
    return Array.from(refs).map((ref, index) => ({
      id: ref.getAttribute('id') || `ref-${index + 1}`,
      content: ref.textContent?.trim() || ''
    }));
  }
}

/**
 * 通用的XML校验工具类
 */
export class XMLValidationHelper {
  
  /**
   * 批量校验多个XML文件
   */
  static async validateMultipleFiles(files: File[]): Promise<{
    fileName: string;
    isValid: boolean;
    errorCount: number;
    warningCount: number;
    summary: string;
  }[]> {
    
    const results = [];
    
    for (const file of files) {
      try {
        const content = await this.readFileContent(file);
        const validation = jatsValidator.validate(content);
        
        results.push({
          fileName: file.name,
          isValid: validation.isValid,
          errorCount: validation.errors.length,
          warningCount: validation.warnings.length,
          summary: validation.isValid ? 
            '校验通过' : 
            `${validation.errors.length}个错误，${validation.warnings.length}个警告`
        });
        
      } catch (error) {
        results.push({
          fileName: file.name,
          isValid: false,
          errorCount: 1,
          warningCount: 0,
          summary: `读取文件失败: ${error.message}`
        });
      }
    }
    
    return results;
  }
  
  /**
   * 生成校验报告
   */
  static generateValidationReport(xmlContent: string): string {
    const report = jatsValidator.getDetailedReport(xmlContent);
    
    let reportText = `JATS XML校验报告\n`;
    reportText += `生成时间: ${new Date().toLocaleString()}\n`;
    reportText += `处理时间: ${report.summary.processingTime.toFixed(2)}ms\n\n`;
    
    reportText += `总体状态: ${report.isValid ? '✅ 有效' : '❌ 无效'}\n`;
    reportText += `错误数量: ${report.errors.length}\n`;
    reportText += `警告数量: ${report.warnings.length}\n\n`;
    
    if (report.errors.length > 0) {
      reportText += `错误详情:\n`;
      report.errors.forEach((error, index) => {
        reportText += `${index + 1}. [${error.code}] ${error.message}`;
        if (error.line) reportText += ` (第${error.line}行)`;
        reportText += `\n`;
      });
      reportText += `\n`;
    }
    
    if (report.warnings.length > 0) {
      reportText += `警告详情:\n`;
      report.warnings.forEach((warning, index) => {
        reportText += `${index + 1}. [${warning.code}] ${warning.message}`;
        if (warning.line) reportText += ` (第${warning.line}行)`;
        reportText += `\n`;
      });
    }
    
    return reportText;
  }
  
  private static readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  }
}

// 使用示例
export function demonstrateUsage() {
  console.log('JATS XML校验器使用示例');
  
  // 示例1: 基本校验
  const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
    <article article-type="research-article">
      <front>
        <article-meta>
          <title-group>
            <article-title>Test Article</article-title>
          </title-group>
        </article-meta>
      </front>
    </article>`;
  
  const result = jatsValidator.validate(xmlContent);
  console.log('校验结果:', result.isValid ? '有效' : '无效');
  
  // 示例2: 错误摘要
  const summary = jatsValidator.getErrorSummary(xmlContent);
  console.log('错误摘要:', summary);
  
  // 示例3: 生成报告
  const report = XMLValidationHelper.generateValidationReport(xmlContent);
  console.log('详细报告:\n', report);
}
