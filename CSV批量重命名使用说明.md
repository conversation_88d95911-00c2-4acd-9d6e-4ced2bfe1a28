# CSV批量重命名INDD文件 - 使用说明

## 功能概述

这个功能允许您根据CSV文件中的数据批量重命名本地的INDD文件。重命名逻辑是将CSV中的"编号"添加到原文件名前面。

## 使用步骤

### 1. 准备CSV文件

CSV文件必须包含以下两列：
- **编号列**：包含要添加到文件名前面的编号
- **稿号列**：包含用于匹配INDD文件的稿号

CSV文件示例：
```csv
编号,稿号,其他信息
37,cc-25-44,测试数据1
42,dd-30-55,测试数据2
15,aa-10-20,测试数据3
```

### 2. 上传CSV文件

1. 点击"选择包含编号和稿号的CSV文件"
2. 选择您准备好的CSV文件
3. 系统会自动解析并显示加载的记录数
4. 可以点击"查看CSV数据预览"查看解析结果

### 3. 选择INDD文件目录

1. 点击"选择目录"按钮
2. 选择包含INDD文件的文件夹
3. 可以选择"包含子目录文件"来扫描子文件夹
4. 系统会显示找到的INDD文件数量

### 4. 预览重命名结果

系统会自动生成预览结果，显示：
- 匹配成功的文件（绿色）
- 未匹配的文件（灰色）
- 统计信息（匹配数量、未匹配数量、总文件数）

### 5. 执行重命名

1. 检查预览结果确认无误
2. 可以选择"允许覆盖同名文件"（谨慎使用）
3. 点击"确认重命名"按钮
4. 系统会显示重命名结果

## 重命名逻辑

**原始文件名**：`cc-25-44-finl.indd`
**CSV中编号**：`37`
**CSV中稿号**：`cc-25-44`
**新文件名**：`37-cc-25-44-finl.indd`

系统会：
1. 在INDD文件名中查找CSV中的稿号
2. 如果找到匹配，将编号添加到原文件名前面
3. 如果没有找到匹配，文件保持不变

## 注意事项

1. **备份重要文件**：重命名操作不可逆，建议先备份重要文件
2. **CSV格式**：确保CSV文件使用逗号分隔，包含标题行
3. **文件权限**：确保对目标文件夹有读写权限
4. **稿号匹配**：稿号匹配是大小写不敏感的，支持部分匹配
5. **覆盖设置**：谨慎使用"允许覆盖同名文件"选项

## 故障排除

### CSV文件解析失败
- 检查CSV文件格式是否正确
- 确保包含"编号"和"稿号"列（或包含这些关键词的列名）
- 检查文件编码是否为UTF-8

### 没有找到INDD文件
- 确认选择的目录包含.indd文件
- 如果文件在子目录中，勾选"包含子目录文件"
- 检查文件扩展名是否为.indd

### 文件匹配失败
- 检查CSV中的稿号是否与文件名中的部分匹配
- 稿号应该作为文件名的一部分出现
- 检查稿号是否包含特殊字符

### 重命名失败
- 检查文件是否被其他程序占用
- 确认有足够的磁盘空间
- 检查目标文件夹的写入权限

## 技术支持

如果遇到问题，请检查浏览器控制台的错误信息，或联系技术支持。
