import { createSignal, Show, For, onMount } from 'solid-js';
import type { JSX } from 'solid-js';
import { searchAuthors, searchAuthorsByPMIDs } from './api';
import { AuthorDetail, AuthorDetailFoPubmed, SearchProgress, SearchResult } from './types';
import { initJCRDatabase } from './utils';
import { PubmedService } from './pubmedApi';
import { jcrDatabase } from '@/services/JcrDatabase';
import { extractKeywordsFromDocx, filterAuthorsByJCR } from './utils';
// db.pragma('journal_mode = WAL');
const ReviewerFinder = () => {
  const [loading, setLoading] = createSignal(false);
  const [keyword, setKeyword] = createSignal('');
  const [progress, setProgress] = createSignal(0);
  const [processingTotal, setProcessingTotal] = createSignal(0);
  const [currentProcessing, setCurrentProcessing] = createSignal(0);
  const [currentStage, setCurrentStage] = createSignal<string>('');
  const [subStage, setSubStage] = createSignal<string>('');
  const [yearRange, setYearRange] = createSignal(3);
  const [jcrFilter, setJcrFilter] = createSignal(['Q1', 'Q2', 'Q3']);
  const [limit, setLimit] = createSignal(10);
  const [currentPage, setCurrentPage] = createSignal(1);
  const [searchResults, setSearchResults] = createSignal<SearchResult>({ authors: [], total: 0 });
  const [error, setError] = createSignal<string | null>(null);
  const [pmids, setPmids] = createSignal<string[]>([]);
  const [reviewers, setReviewers] = createSignal<any[]>([]);
  const [filteredReviewers, setFilteredReviewers] = createSignal<AuthorDetailFoPubmed[]>([]);
  const [originalResults, setOriginalResults] = createSignal<AuthorDetailFoPubmed[]>([]);
  const [totalResults, setTotalResults] = createSignal(0);
  const [pageSize, setPageSize] = createSignal(10);

  const downloadCsv = () => {
    const headers = ['作者', '邮箱', 'PMID', 'JCR分区', '文章关键词', 'MESH关键词', '杂志名称'];
    const data = reviewers().map((author: AuthorDetailFoPubmed) => [
      `${author.firstName} ${author.lastName}`,
      author.email || '',
      author.publications[0]?.pmid || "",
      author.publications[0]?.IFQuartile ||"",
      author.expertise?.join("; "),
      author.mesh?.join("; "),
      author.publications[0]?.journal ||""
    ]);
    const csv = [headers, ...data].map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'reviewers.csv';
    link.click();
  };

  const updateProgress = (progress: SearchProgress) => {
    setCurrentProcessing(progress.current);
    setProcessingTotal(progress.total);
    setProgress((progress.current / progress.total) * 100);
    setCurrentStage(progress.stage);
    setSubStage(progress.subStage || '');
  };

  const handleFileUpload: JSX.EventHandler<HTMLInputElement, Event> = async (event) => {
    const file = event.currentTarget.files?.[0];
    if (file && file.name.endsWith('.docx')) {
      try {
        setLoading(true);
        const keywords = await extractKeywordsFromDocx(file);
        setKeyword(keywords);
      } catch (error) {
        setError('文件解析失败');
      } finally {
        setLoading(false);
      }
    }
  };

  const applyJCRFilter = () => {
    const filtered = filterAuthorsByJCR(originalResults(), jcrFilter());
    setReviewers(filtered);
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      setError(null);
      let pubmedResults = await searchPubmed({ keywords: keyword() });
      let filterResults = pubmedResults?.filter(item => item.email) ?? [];
      filterResults = filterResults.map(item => {
        item.publications[0].IFQuartile = jcrDatabase.getJournalQuartile(item.publications[0].issn)
        return item;
      });
      
      setOriginalResults(filterResults);
      applyJCRFilter();
      
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getTotalPages = () => Math.ceil(totalResults() / pageSize());

  const searchPubmed = async (searchParams: any) => {
    try {
      let allResults: any[] = [];
      let total = 0;
      let page = currentPage();
  
      while (true) {
        let currentPageResults;
        if (searchParams.keywords) {
          const { results, total: totalCount } = await PubmedService.searchByKeywords(
            searchParams.keywords, 
            page,
            pageSize()
          );
          currentPageResults = results;
          if (page === 1) {
            setTotalResults(totalCount);
          }
        } else if (searchParams.pmid) {
          currentPageResults = await PubmedService.searchByPMID(searchParams.pmid);
        }

        if (!currentPageResults || currentPageResults.length === 0) {
          break;
        }
  
        const validResults = currentPageResults.filter(item => item.email);
        allResults = [...allResults, ...validResults];
  
        if (allResults.length >= limit() || currentPageResults.length === 0) {
          break;
        }
  
        page++;
        setCurrentPage(page);
      }
  
      return allResults;
    } catch (error) {
      console.error('PubMed查询失败:', error);
      throw error;
    }
  };

  onMount(async () => {

    await jcrDatabase.init(chrome.runtime.getURL('assets/jcr.db'));
    //     const row:any = db.prepare('SELECT * FROM JCR2023 WHERE eissn = ?').get("2649-6100");
    // console.log(row.journal);
    // const IFQuartile = jcrDatabase.getJournalQuartile("LANCET")
    // console.log(IFQuartile)
  });

  const PaginationControls = () => (
    <div class="flex justify-center items-center gap-2 mt-4">
      <button
        class="btn btn-sm"
        disabled={currentPage() === 1}
        onClick={() => {
          setCurrentPage(prev => Math.max(1, prev - 1));
          handleSearch();
        }}
      >
        上一页
      </button>
      <span>
        第 {currentPage()} 页 / 共 {getTotalPages()} 页
        (总计 {totalResults()} 条记录)
      </span>
      <button
        class="btn btn-sm"
        disabled={currentPage() >= getTotalPages()}
        onClick={() => {
          setCurrentPage(prev => prev + 1);
          handleSearch();
        }}
      >
        下一页
      </button>
    </div>
  );

  return (
    <div class="space-y-6">
      <h1 class="text-2xl font-bold">查找审稿人 (PubMed)</h1>

      <div class="form-control space-y-4">
        <div class="flex gap-4">
          <div class="flex-1 flex gap-2">
            <input
              type="text"
              placeholder="输入关键词搜索"
              class="input input-bordered flex-1"
              value={keyword()}
              onInput={(e) => setKeyword(e.currentTarget.value)}
            />
            <input
              type="file"
              accept=".docx"
              class="file-input file-input-bordered file-input-md w-52"
              onChange={handleFileUpload}
            />
          </div>
          <select
            name='year'
            class="select select-bordered w-32"
            value={yearRange()}
            onChange={(e) => setYearRange(parseInt(e.currentTarget.value))}
          >
            <option value="3">近3年</option>
            <option value="5">近5年</option>
            <option value="10">近10年</option>
          </select>
          <select
            name='limit'
            class="select select-bordered w-32"
            value={limit()}
            onChange={(e) => setLimit(parseInt(e.currentTarget.value))}
          >
            <option value="10">10条</option>
            <option value="20">20条</option>
            <option value="30">30条</option>
            <option value="50">50条</option>
            <option value="80">80条</option>
          </select>
          <button
            class="btn btn-primary"
            disabled={loading() || !keyword().trim()}
            onClick={handleSearch}
          >
            搜索
          </button>
        </div>

        <div class="flex gap-2">
          <span class="text-sm">JCR分区:</span>
          <For each={['Q1', 'Q2', 'Q3', 'Q4']}>
            {(quarter) => (
              <label class="cursor-pointer">
                <input
                  type="checkbox"
                  class="checkbox checkbox-sm"
                  checked={jcrFilter().includes(quarter)}
                  onChange={(e) => {
                    if (e.currentTarget.checked) {
                      setJcrFilter([...jcrFilter(), quarter]);
                    } else {
                      setJcrFilter(jcrFilter().filter(q => q !== quarter));
                    }
                    applyJCRFilter(); // 添加这行来实时更新过滤结果
                  }}
                />
                <span class="ml-1">{quarter}</span>
              </label>
            )}
          </For>
        </div>

      </div>

      <Show when={loading()}>
        <div class="w-full">
          <div class="relative pt-1">
            <div class="flex mb-2 items-center justify-between">
             
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-blue-600">
                  {currentProcessing()}/{processingTotal()}
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
              <div
                style={{ width: `${progress()}%` }}
                class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 transition-all duration-500"
              />
            </div>
          </div>
        </div>
      </Show>

      <Show when={reviewers().length > 0}>
        <div class="overflow-x-auto">
          <table class="table w-full">
            <thead>
              <tr>
                <th>序号</th>
                <th>作者</th>
                <th>邮箱</th>
                <th>pmid</th>
                <th>最新文章JCR</th>
                <th>文章关键词</th>
                <th>Mesh</th>
                <th>杂志名称</th>
              </tr>
            </thead>
            <tbody>
              <For each={reviewers()}>
                {(author: AuthorDetailFoPubmed,key) => (
                  <tr>
                    <td>{key()+1}</td>
                    <td>{`${author.firstName} ${author.lastName}`}</td>
                    <td>{author.email || '-'}</td>
                    <td>{author.publications[0]?.pmid || ""}</td>
                    <td>{author.publications[0]?.IFQuartile ||""}</td>
                    <td>{author.expertise?.join("; ")}</td>
                    <td>{author.mesh?.join("; ")}</td>
                    <td>{author.publications[0]?.journal ||""}</td>
                  </tr>
                )}
              </For>
            </tbody>
          </table>
        </div>
        <PaginationControls />
        <div class="text-right">
          <button
            class="btn btn-success btn-sm"
            onClick={downloadCsv}
          >
            下载 CSV
          </button>
        </div>
      </Show>
    </div>
  );
};

export default ReviewerFinder;
