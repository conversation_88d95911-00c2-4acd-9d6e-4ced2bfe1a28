// 专门针对demo.xml文件的测试和校验

import { jatsValidator } from '../index';
import { JATSAutoFixer } from '../autoFixer';

/**
 * 从demo.xml文件读取内容并进行校验测试
 */
export async function testDemoXMLFile(): Promise<{
  success: boolean;
  validationResult: any;
  fixResult?: any;
  errors: string[];
}> {
  try {
    // 这里应该读取实际的demo.xml文件
    // 由于无法直接读取文件，我们使用一个简化的demo内容进行测试
    const demoXMLContent = await getDemoXMLContent();
    
    console.log('🧪 开始测试demo.xml文件校验...');
    
    // 1. 首次校验
    const validationResult = jatsValidator.validate(demoXMLContent);
    
    console.log(`📊 初始校验结果:`);
    console.log(`   状态: ${validationResult.isValid ? '✅ 有效' : '❌ 无效'}`);
    console.log(`   错误: ${validationResult.errors.length} 个`);
    console.log(`   警告: ${validationResult.warnings.length} 个`);
    
    // 2. 如果有错误，尝试自动修复
    let fixResult = null;
    if (!validationResult.isValid) {
      console.log('\n🔧 尝试自动修复...');
      const fixer = new JATSAutoFixer();
      fixResult = fixer.autoFix(demoXMLContent);
      
      if (fixResult.fixed) {
        console.log(`✨ 应用了 ${fixResult.appliedFixes.length} 个修复`);
        fixResult.appliedFixes.forEach((fix, index) => {
          console.log(`   ${index + 1}. ${fix}`);
        });
        
        // 重新校验修复后的内容
        const fixedValidationResult = jatsValidator.validate(fixResult.fixedXML);
        console.log(`\n📊 修复后校验结果:`);
        console.log(`   状态: ${fixedValidationResult.isValid ? '✅ 有效' : '❌ 无效'}`);
        console.log(`   错误: ${fixedValidationResult.errors.length} 个`);
        console.log(`   警告: ${fixedValidationResult.warnings.length} 个`);
      }
    }
    
    return {
      success: true,
      validationResult,
      fixResult,
      errors: []
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('❌ 测试过程中发生错误:', errorMessage);
    
    return {
      success: false,
      validationResult: null,
      errors: [errorMessage]
    };
  }
}

/**
 * 获取demo.xml的内容（简化版本，基于实际文件结构）
 */
async function getDemoXMLContent(): Promise<string> {
  // 这是基于实际demo.xml文件的简化版本
  return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">
<article article-type="research-article" dtd-version="1.2" xml:lang="en" xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">QIMS</journal-id>
      <journal-id journal-id-type="nlm-ta">Quant Imaging Med Surg</journal-id>
      <journal-title-group>
        <journal-title>Quantitative Imaging in Medicine and Surgery</journal-title>
        <abbrev-journal-title abbrev-type="pubmed">Quant. Imaging Med. Surg.</abbrev-journal-title>
      </journal-title-group>
      <issn pub-type="ppub">2223-4292</issn>
      <issn pub-type="epub">2223-4306</issn>
      <publisher>
        <publisher-name>AME Publishing Company</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="publisher-id">qims-24-2439</article-id>
      <article-id pub-id-type="doi">10.21037/qims-24-2439</article-id>
      <article-categories>
        <subj-group subj-group-type="heading">
          <subject>Original Article</subject>
        </subj-group>
      </article-categories>
      <title-group>
        <article-title>Cervical sliding sign and cervical funneling in the third trimester as predictors of spontaneous preterm birth in singleton pregnancy</article-title>
      </title-group>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Zhang</surname>
            <given-names>Miaomiao</given-names>
          </name>
          <xref ref-type="aff" rid="aff1">
            <sup>1</sup>
          </xref>
        </contrib>
        <contrib contrib-type="author" corresp="yes">
          <name>
            <surname>Yu</surname>
            <given-names>Hongkui</given-names>
          </name>
          <xref ref-type="aff" rid="aff1">
            <sup>1</sup>
          </xref>
        </contrib>
      </contrib-group>
      <aff id="aff1">
        <label>1</label>
        <institution content-type="dept">Department of Ultrasonography</institution>, 
        <institution>Shenzhen Baoan Women's and Children's Hospital</institution>, 
        <addr-line>Shenzhen</addr-line>, 
        <country country="cn">China</country>
      </aff>
      <author-notes>
        <fn id="afn1">
          <p><italic>Contributions:</italic> (I) Conception and design: H Yu, Y Liu, M Zhang</p>
        </fn>
        <corresp id="cor1">
          <italic>Correspondence to:</italic> Hongkui Yu, PhD. 
          Email: <email xlink:href="<EMAIL>"><EMAIL></email>.
        </corresp>
      </author-notes>
      <pub-date pub-type="epub">
        <day>30</day>
        <month>06</month>
        <year>2025</year>
      </pub-date>
      <volume>15</volume>
      <issue>7</issue>
      <fpage>6005</fpage>
      <lpage>6015</lpage>
      <history>
        <date date-type="received">
          <day>04</day>
          <month>11</month>
          <year>2024</year>
        </date>
      </history>
      <permissions>
        <copyright-statement>© 2025 AME Publishing Company. All rights reserved.</copyright-statement>
        <copyright-year>2025</copyright-year>
        <copyright-holder>AME Publishing Company.</copyright-holder>
        <license xlink:href="http://creativecommons.org/licenses/by-nc-nd/4.0/">
          <license-p><italic>Open Access Statement:</italic> This is an Open Access article. 
          See: <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by-nc-nd/4.0/">https://creativecommons.org/licenses/by-nc-nd/4.0</ext-link>.</license-p>
        </license>
      </permissions>
      <abstract>
        <sec>
          <title>Background</title>
          <p>Spontaneous preterm birth (sPTB) is a major cause of neonatal morbidity and mortality.</p>
        </sec>
      </abstract>
      <kwd-group kwd-group-type="author">
        <title>Keywords: </title>
        <kwd>Cervical sliding sign (CSS)</kwd>
        <kwd>funneling</kwd>
      </kwd-group>
      <custom-meta-group>
        <custom-meta>
          <meta-name>OPEN-ACCESS</meta-name>
          <meta-value>TRUE</meta-value>
        </custom-meta>
      </custom-meta-group>
    </article-meta>
  </front>
  <body>
    <sec sec-type="intro">
      <title>Introduction</title>
      <p>Spontaneous preterm birth (sPTB) is defined as an unplanned birth before 37 complete weeks of gestation.</p>
    </sec>
  </body>
  <back>
    <ref-list>
      <ref id="r1">
        <label>1</label>
        <mixed-citation publication-type="journal">
          <person-group person-group-type="author">
            <name>
              <surname>Smith</surname>
              <given-names>J</given-names>
            </name>
          </person-group>. 
          <article-title>Example article</article-title> 
          <source>Example Journal</source> 
          <year>2024</year>;
          <volume>1</volume>:
          <fpage>1</fpage>-
          <lpage>10</lpage>.
        </mixed-citation>
      </ref>
    </ref-list>
  </back>
</article>`;
}

/**
 * 生成符合demo.xml标准的XML代码模板
 */
export function generateStandardJATSTemplate(): string {
  return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">
<article article-type="research-article" dtd-version="1.2" xml:lang="en" 
    xmlns:mml="http://www.w3.org/1998/Math/MathML" 
    xmlns:xlink="http://www.w3.org/1999/xlink" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">JOURNAL_ID</journal-id>
      <journal-title-group>
        <journal-title>Journal Title</journal-title>
      </journal-title-group>
      <issn pub-type="ppub">0000-0000</issn>
      <publisher>
        <publisher-name>Publisher Name</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="publisher-id">article-id</article-id>
      <title-group>
        <article-title>Article Title</article-title>
      </title-group>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Author</surname>
            <given-names>First</given-names>
          </name>
        </contrib>
      </contrib-group>
      <pub-date pub-type="epub">
        <year>2024</year>
      </pub-date>
      <abstract>
        <p>Abstract content here.</p>
      </abstract>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>Content here.</p>
    </sec>
  </body>
</article>`;
}

/**
 * 验证XML是否符合demo.xml标准
 */
export function validateAgainstDemoStandard(xmlContent: string): {
  isCompliant: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  // 检查基本结构
  if (!xmlContent.includes('<?xml version="1.0" encoding="UTF-8"')) {
    issues.push('缺少标准的XML声明');
    suggestions.push('添加: <?xml version="1.0" encoding="UTF-8" standalone="no"?>');
  }
  
  if (!xmlContent.includes('<!DOCTYPE article PUBLIC "-//NLM//DTD JATS')) {
    issues.push('缺少JATS DTD声明');
    suggestions.push('添加标准的JATS DTD声明');
  }
  
  if (!xmlContent.includes('xmlns:mml="http://www.w3.org/1998/Math/MathML"')) {
    issues.push('缺少MathML命名空间');
    suggestions.push('在article元素中添加MathML命名空间');
  }
  
  if (!xmlContent.includes('xmlns:xlink="http://www.w3.org/1999/xlink"')) {
    issues.push('缺少XLink命名空间');
    suggestions.push('在article元素中添加XLink命名空间');
  }
  
  if (!xmlContent.includes('<journal-meta>')) {
    issues.push('缺少journal-meta元素');
    suggestions.push('在front元素中添加journal-meta');
  }
  
  if (!xmlContent.includes('<article-meta>')) {
    issues.push('缺少article-meta元素');
    suggestions.push('在front元素中添加article-meta');
  }
  
  return {
    isCompliant: issues.length === 0,
    issues,
    suggestions
  };
}

// 导出测试函数
export const demoXMLTests = {
  testDemoXMLFile,
  generateStandardJATSTemplate,
  validateAgainstDemoStandard
};
