chrome.runtime.onInstalled.addListener(() => {
    console.log("扩展已安装");
});


// 处理扩展图标点击
chrome.action.onClicked.addListener(() => {
    chrome.tabs.create({ 
        url: chrome.runtime.getURL("index.html") 
    });
});

chrome.action.onClicked.addListener((tab) => {
    const url = new URL(tab.url);
    const domain = "webofscience.clarivate.cn";
  
    chrome.cookies.getAll({ domain }, (cookies) => {
      console.log(`Cookies for domain ${domain}:`, cookies);
      // 你可以在这里处理获取到的 cookies
    });
  });

chrome.webRequest.onBeforeSendHeaders.addListener(
(details) => {
    if (details.url.includes('webofscience.clarivate.cn/api/')) {
    const headers = details.requestHeaders || [];
    
    // 设置 Origin
    headers.push({
        name: 'Origin',
        value: 'https://webofscience.clarivate.cn'
    });

    // 设置 Referer
    headers.push({
        name: 'Referer',
        value: 'https://webofscience.clarivate.cn/wos/woscc/summary/summary'
    });

    // 如果URL包含特定的记录ID，则修改Referer
    const matchWosId = details.url.match(/full-record\/(\w+)/);
    if (matchWosId) {
        const wosId = matchWosId[1];
        headers.forEach(header => {
        if (header.name.toLowerCase() === 'referer') {
            header.value = `https://webofscience.clarivate.cn/wos/woscc/full-record/${wosId}`;
        }
        });
    }

    return { requestHeaders: headers };
    }
},
{
    urls: ["*://webofscience.clarivate.cn/*"]
},
["requestHeaders", "extraHeaders", "blocking"]
);