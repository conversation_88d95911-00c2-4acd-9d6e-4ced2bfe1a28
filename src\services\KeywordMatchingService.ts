/**
 * 关键词匹配算法服务
 * 提供基于关键词重合度和词义相似度的文章排序功能
 */

export class KeywordMatchingService {
  
  /**
   * 计算两个字符串的编辑距离（Levenshtein距离）
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }
    
    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 计算字符串相似度（基于编辑距离）
   */
  private static stringSimilarity(str1: string, str2: string): number {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;
    
    const distance = this.levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
    return (maxLength - distance) / maxLength;
  }

  /**
   * 提取文本中的关键词（简单的词干提取）
   */
  private static extractKeywords(text: string): string[] {
    // 移除标点符号，转换为小写，分割单词
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2); // 过滤掉长度小于3的单词
    
    // 移除常见停用词
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
      'from', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among'
    ]);
    
    return words.filter(word => !stopWords.has(word));
  }

  /**
   * 计算关键词在文本中的匹配分数
   */
  private static calculateKeywordMatch(keyword: string, text: string): KeywordMatch {
    const normalizedKeyword = keyword.toLowerCase().trim();
    const normalizedText = text.toLowerCase();
    
    // 精确匹配
    if (normalizedText.includes(normalizedKeyword)) {
      return {
        keyword,
        score: 1.0,
        type: 'exact'
      };
    }
    
    // 部分匹配 - 检查关键词的各个部分
    const keywordParts = normalizedKeyword.split(/\s+/);
    let partialMatches = 0;
    
    for (const part of keywordParts) {
      if (normalizedText.includes(part)) {
        partialMatches++;
      }
    }
    
    if (partialMatches > 0) {
      const partialScore = partialMatches / keywordParts.length * 0.8;
      return {
        keyword,
        score: partialScore,
        type: 'partial'
      };
    }
    
    // 语义相似度匹配
    const textWords = this.extractKeywords(text);
    let maxSimilarity = 0;
    
    for (const word of textWords) {
      const similarity = this.stringSimilarity(normalizedKeyword, word);
      maxSimilarity = Math.max(maxSimilarity, similarity);
    }
    
    // 只有相似度超过阈值才认为是语义匹配
    if (maxSimilarity > 0.7) {
      return {
        keyword,
        score: maxSimilarity * 0.6,
        type: 'semantic'
      };
    }
    
    return {
      keyword,
      score: 0,
      type: 'semantic'
    };
  }

  /**
   * 计算文章的关键词匹配分数（基于综合命中率）
   */
  static calculateArticleScore(article: PubMedArticle, keywords: string[]): ScoredArticle {
    const keywordMatches: KeywordMatch[] = [];
    let hitCount = 0; // 命中的关键词数量

    // 构建用于匹配的文本内容
    const searchableText = [
      article.title,
      article.abstract || '',
      ...(article.keywords || []),
      article.journal,
      ...article.authors.map(author => `${author.forename} ${author.lastname}`),
      ...(article.affiliations || [])
    ].join(' ');

    // 计算每个关键词的匹配情况
    for (const keyword of keywords) {
      const match = this.calculateKeywordMatch(keyword, searchableText);
      keywordMatches.push(match);

      // 只要有匹配就算命中（分数大于0）
      if (match.score > 0) {
        hitCount++;
      }
    }

    // 计算个数命中率（命中的关键词数 / 总关键词数）
    const hitRate = keywords.length > 0 ? hitCount / keywords.length : 0;

    // 计算综合命中率：个数命中率 * 所有单个相似率的乘积
    let comprehensiveHitRate = 0;
    if (hitCount > 0) {
      // 获取所有命中关键词的相似率
      const hitScores = keywordMatches.filter(match => match.score > 0).map(match => match.score);
      
      if (hitScores.length > 0) {
        // 计算所有单个相似率的乘积
        const similarityProduct = hitScores.reduce((product, score) => product * score, 1);
        // 综合命中率 = 个数命中率 * 单个相似率乘积
        comprehensiveHitRate = hitRate * similarityProduct;
      }
    }

    // 计算加权分数作为次要排序依据
    let weightedScore = 0;
    for (const match of keywordMatches) {
      if (match.score > 0) {
        let score = match.score;
        if (match.type === 'exact') {
          score *= 2.0; // 精确匹配权重最高
        } else if (match.type === 'partial') {
          score *= 1.5; // 部分匹配权重中等
        }
        weightedScore += score;
      }
    }

    // 标题匹配额外加分
    const titleMatches = keywords.filter(keyword =>
      article.title.toLowerCase().includes(keyword.toLowerCase())
    ).length;
    if (titleMatches > 0) {
      weightedScore += titleMatches * 0.5;
    }

    // 关键词字段匹配额外加分
    if (article.keywords) {
      const keywordFieldMatches = keywords.filter(keyword =>
        article.keywords!.some(articleKeyword =>
          articleKeyword.toLowerCase().includes(keyword.toLowerCase())
        )
      ).length;
      if (keywordFieldMatches > 0) {
        weightedScore += keywordFieldMatches * 0.3;
      }
    }

    // 计算优先级加分：关键词 -> 邮箱 -> ORCID
    let priorityBonus = 0;

    // 关键词匹配加分（最高优先级）
    if (hitCount > 0) {
      priorityBonus += 1000; // 关键词匹配基础分
    }

    // 邮箱加分（第二优先级）
    const hasEmail = article.authors.some(author => author.email);
    if (hasEmail) {
      priorityBonus += 100;
    }

    // ORCID 加分（第三优先级）
    const hasOrcid = article.authors.some(author => author.orcid);
    if (hasOrcid) {
      priorityBonus += 10;
    }

    // 最终相关性分数：优先级加分 + 综合命中率 * 1000 + 归一化的加权分数
    const normalizedWeightedScore = Math.min(weightedScore / keywords.length, 1.0);
    const relevanceScore = priorityBonus + (comprehensiveHitRate * 1000) + normalizedWeightedScore;

    return {
      ...article,
      relevanceScore,
      keywordMatches,
      hitRate, // 个数命中率字段用于显示
      hitCount, // 命中数量字段用于显示
      comprehensiveHitRate // 综合命中率字段用于显示
    };
  }

  /**
   * 对文章列表按综合命中率排序
   */
  static rankArticles(articles: PubMedArticle[], keywords: string[]): ScoredArticle[] {
    if (!keywords || keywords.length === 0) {
      return articles.map(article => ({
        ...article,
        relevanceScore: 0,
        keywordMatches: [],
        comprehensiveHitRate: 0
      }));
    }

    // 计算每篇文章的分数
    const scoredArticles = articles.map(article =>
      this.calculateArticleScore(article, keywords)
    );

    // 按综合命中率降序排序，综合命中率相同时按相关性分数排序
    return scoredArticles.sort((a, b) => {
      if (b.comprehensiveHitRate !== a.comprehensiveHitRate) {
        return b.comprehensiveHitRate! - a.comprehensiveHitRate!;
      }
      return b.relevanceScore - a.relevanceScore;
    });
  }

  /**
   * 快速预筛选文章（基于标题和摘要的简单匹配）
   * 用于在获取详细信息前进行初步过滤
   */
  static preFilterArticles(articles: any[], keywords: string[], maxResults: number = 200): any[] {
    if (!keywords || keywords.length === 0) {
      return articles.slice(0, maxResults);
    }

    const keywordLower = keywords.map(k => k.toLowerCase());

    // 计算简单的匹配分数
    const scoredArticles = articles.map(article => {
      const title = (article.title || '').toLowerCase();
      const abstract = (article.abstract || '').toLowerCase();
      const searchText = `${title} ${abstract}`;

      let score = 0;
      let matchCount = 0;

      keywordLower.forEach(keyword => {
        if (searchText.includes(keyword)) {
          matchCount++;
          // 标题匹配权重更高
          if (title.includes(keyword)) {
            score += 2;
          } else {
            score += 1;
          }
        }
      });

      return {
        ...article,
        preScore: score,
        preMatchCount: matchCount
      };
    });

    // 按匹配数量和分数排序，取前N个
    return scoredArticles
      .filter(article => article.preMatchCount > 0) // 至少匹配一个关键词
      .sort((a, b) => {
        if (b.preMatchCount !== a.preMatchCount) {
          return b.preMatchCount - a.preMatchCount; // 匹配数量优先
        }
        return b.preScore - a.preScore; // 然后按分数
      })
      .slice(0, maxResults);
  }

  /**
   * 从文档内容中提取关键词
   */
  static extractKeywordsFromText(text: string, maxKeywords: number = 10): string[] {
    const words = this.extractKeywords(text);
    
    // 计算词频
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    });
    
    // 按频率排序并返回前N个关键词
    return Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxKeywords)
      .map(([word]) => word);
  }

  /**
   * 高亮显示匹配的关键词
   */
  static highlightKeywords(text: string, keywords: string[]): string {
    let highlightedText = text;
    
    keywords.forEach(keyword => {
      const regex = new RegExp(`(${keyword})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });
    
    return highlightedText;
  }
}
