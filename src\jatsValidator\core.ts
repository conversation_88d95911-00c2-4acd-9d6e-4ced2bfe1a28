// JATS XML校验器核心模块

import { ValidationError, ValidationResult, ValidatorOptions } from './types';
import { JATS_RULES, JATS_DTD_DECLARATIONS } from './rules';

export class JATSValidator {
  private options: ValidatorOptions;
  private errors: ValidationError[] = [];
  private warnings: ValidationError[] = [];
  private info: ValidationError[] = [];

  constructor(options: ValidatorOptions = {}) {
    this.options = {
      checkWellFormedness: true,
      checkJATSCompliance: true,
      checkStructure: true,
      checkAttributes: true,
      strictMode: false,
      maxErrors: 100,
      ...options
    };
  }

  /**
   * 格式化XML内容
   */
  public formatXML(xmlContent: string): string {
    try {
      // 移除多余的空白字符和换行
      let formatted = xmlContent.trim();

      // 解析XML以确保格式正确
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(formatted, 'text/xml');

      // 检查解析错误
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        // 如果解析失败，返回原始内容
        return formatted;
      }

      // 使用XMLSerializer重新序列化
      const serializer = new XMLSerializer();
      const serialized = serializer.serializeToString(xmlDoc);

      // 格式化XML内容
      return this.prettifyXML(serialized);

    } catch (error) {
      // 如果格式化失败，返回原始内容
      return xmlContent.trim();
    }
  }

  /**
   * 美化XML格式
   */
  private prettifyXML(xmlString: string): string {
    const PADDING = '  '; // 使用2个空格作为缩进
    const reg = /(>)(<)(\/*)/g;
    let formatted = xmlString.replace(reg, '$1\n$2$3');

    let pad = 0;
    return formatted.split('\n').map((line) => {
      let indent = 0;
      if (line.match(/.+<\/\w[^>]*>$/)) {
        indent = 0;
      } else if (line.match(/^<\/\w/) && pad > 0) {
        pad -= 1;
      } else if (line.match(/^<\w[^>]*[^\/]>.*$/)) {
        indent = 1;
      } else {
        indent = 0;
      }

      const padding = PADDING.repeat(pad);
      pad += indent;

      return padding + line;
    }).join('\n');
  }

  /**
   * 校验XML字符串
   */
  public validate(xmlContent: string): ValidationResult {
    const startTime = performance.now();
    this.resetErrors();

    try {
      // 0. 预处理：格式化XML内容
      const formattedXML = this.formatXML(xmlContent);

      // 1. 检查XML格式正确性
      if (this.options.checkWellFormedness) {
        this.checkWellFormedness(formattedXML);
      }

      // 2. 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(formattedXML, 'text/xml');
      
      // 检查解析错误
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        this.addError('error', 'XML_PARSE_ERROR', parseError.textContent || 'XML解析失败');
        return this.buildResult(startTime);
      }

      // 3. 检查JATS合规性
      if (this.options.checkJATSCompliance) {
        this.checkJATSCompliance(formattedXML, xmlDoc);
      }

      // 4. 检查结构
      if (this.options.checkStructure) {
        this.checkStructure(xmlDoc);
      }

      // 5. 检查属性
      if (this.options.checkAttributes) {
        this.checkAttributes(xmlDoc);
      }

    } catch (error) {
      this.addError('error', 'VALIDATION_ERROR', `校验过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
    }

    return this.buildResult(startTime);
  }

  /**
   * 检查XML格式正确性
   */
  private checkWellFormedness(xmlContent: string): void {
    // 检查基本的XML格式问题
    
    // 检查XML声明
    if (!xmlContent.trim().startsWith('<?xml')) {
      this.addWarning('XML_DECLARATION_MISSING', '缺少XML声明');
    }

    // 检查未闭合的标签
    this.checkUnclosedTags(xmlContent);
    
    // 检查标签配对
    this.checkTagPairing(xmlContent);
    
    // 检查特殊字符转义
    this.checkCharacterEscaping(xmlContent);
  }

  /**
   * 检查未闭合的标签
   */
  private checkUnclosedTags(xmlContent: string): void {
    // 使用更精确的标签匹配，不依赖行号
    const tagStack: Array<{name: string, position: number, line: number}> = [];

    // 移除注释和CDATA，避免干扰
    let cleanContent = xmlContent.replace(/<!--[\s\S]*?-->/g, '');
    cleanContent = cleanContent.replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, '');

    // 匹配所有标签的正则表达式
    const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9:-]*)[^>]*\/?>/g;
    let match;

    while ((match = tagRegex.exec(cleanContent)) !== null) {
      const fullTag = match[0];
      const tagName = match[1];
      const position = match.index;
      const line = this.getLineNumber(xmlContent, position);

      // 跳过特殊标签（XML声明、DTD、处理指令等）
      if (this.isSpecialTag(tagName) || fullTag.startsWith('<?') || fullTag.startsWith('<!')) {
        continue;
      }

      // 自闭合标签
      if (fullTag.endsWith('/>') || this.isSelfClosingTag(tagName)) {
        continue;
      }

      // 结束标签
      if (fullTag.startsWith('</')) {
        const lastOpen = tagStack.pop();

        if (!lastOpen) {
          this.addError('error', 'UNEXPECTED_CLOSING_TAG',
            `第${line}行: 意外的结束标签 </${tagName}>`, line);
        } else if (lastOpen.name !== tagName) {
          // 检查是否是嵌套错误
          const matchingIndex = this.findMatchingOpenTag(tagStack, tagName);
          if (matchingIndex !== -1) {
            // 找到匹配的开始标签，但中间有未闭合的标签
            const unClosedTags = tagStack.splice(matchingIndex);
            unClosedTags.forEach(unclosed => {
              this.addError('error', 'UNCLOSED_TAG',
                `第${unclosed.line}行: 标签 <${unclosed.name}> 未闭合`, unclosed.line);
            });
          } else {
            this.addError('error', 'TAG_MISMATCH',
              `第${line}行: 标签不匹配，期望 </${lastOpen.name}>，但找到 </${tagName}>`, line);
            // 将错误匹配的标签放回栈中
            tagStack.push(lastOpen);
          }
        }
      }
      // 开始标签
      else {
        tagStack.push({ name: tagName, position, line });
      }
    }

    // 检查未闭合的标签
    tagStack.forEach(tag => {
      this.addError('error', 'UNCLOSED_TAG',
        `第${tag.line}行: 标签 <${tag.name}> 未闭合`, tag.line);
    });
  }

  /**
   * 获取指定位置的行号
   */
  private getLineNumber(content: string, position: number): number {
    return content.substring(0, position).split('\n').length;
  }

  /**
   * 在标签栈中查找匹配的开始标签
   */
  private findMatchingOpenTag(tagStack: Array<{name: string, position: number, line: number}>, tagName: string): number {
    for (let i = tagStack.length - 1; i >= 0; i--) {
      if (tagStack[i].name === tagName) {
        return i;
      }
    }
    return -1;
  }

  /**
   * 检查标签配对
   */
  private checkTagPairing(xmlContent: string): void {
    // 检查自闭合标签的格式
    const selfClosingRegex = /<([a-zA-Z][a-zA-Z0-9:-]*)[^>]*\/>/g;
    let match;

    while ((match = selfClosingRegex.exec(xmlContent)) !== null) {
      const fullMatch = match[0];
      const position = match.index;
      const line = this.getLineNumber(xmlContent, position);

      // 检查自闭合标签格式
      if (!fullMatch.endsWith('/>')) {
        this.addError('error', 'INVALID_SELF_CLOSING_TAG',
          `第${line}行: 自闭合标签格式错误 ${fullMatch}`, line);
      }

      // 检查自闭合标签前是否有空格
      if (!fullMatch.match(/\s+\/>/)) {
        this.addWarning('SELF_CLOSING_TAG_SPACING',
          `第${line}行: 建议在自闭合标签的 "/>" 前添加空格`, line);
      }
    }

    // 检查标签的正确嵌套
    this.checkTagNesting(xmlContent);
  }

  /**
   * 检查标签嵌套
   */
  private checkTagNesting(xmlContent: string): void {
    // 检查常见的错误嵌套模式
    const invalidNestingPatterns = [
      // 块级元素不应该嵌套在内联元素中
      { pattern: /<(bold|italic|underline|sup|sub)[^>]*>[\s\S]*?<(p|sec|title|abstract)/,
        message: '内联元素不应包含块级元素' },
      // 检查重叠标签
      { pattern: /<([a-zA-Z][a-zA-Z0-9:-]*)[^>]*>[\s\S]*?<([a-zA-Z][a-zA-Z0-9:-]*)[^>]*>[\s\S]*?<\/\1>[\s\S]*?<\/\2>/,
        message: '标签重叠，应该正确嵌套' }
    ];

    invalidNestingPatterns.forEach(({ pattern, message }) => {
      const matches = xmlContent.match(pattern);
      if (matches) {
        const position = xmlContent.indexOf(matches[0]);
        const line = this.getLineNumber(xmlContent, position);
        this.addWarning('INVALID_NESTING', `第${line}行: ${message}`, line);
      }
    });
  }

  /**
   * 检查字符转义
   */
  private checkCharacterEscaping(xmlContent: string): void {
    // 检查未转义的 & 字符
    this.checkUnescapedAmpersand(xmlContent);

    // 检查未转义的 < 字符
    this.checkUnescapedLessThan(xmlContent);

    // 检查未转义的 > 字符
    this.checkUnescapedGreaterThan(xmlContent);
  }

  /**
   * 检查未转义的 & 字符
   */
  private checkUnescapedAmpersand(xmlContent: string): void {
    // 匹配文本内容中的未转义 & 字符
    const textContentRegex = />([^<]*?)&(?!(amp|lt|gt|quot|apos|#\d+|#x[0-9a-fA-F]+);)([^<]*?)</g;
    let match;

    while ((match = textContentRegex.exec(xmlContent)) !== null) {
      const position = match.index + match[1].length + 1; // & 字符的位置
      const line = this.getLineNumber(xmlContent, position);

      this.addError('error', 'UNESCAPED_AMPERSAND',
        `第${line}行: 文本内容中有未转义的 & 字符，应使用 &amp;`, line);
    }
  }

  /**
   * 检查未转义的 < 字符
   */
  private checkUnescapedLessThan(xmlContent: string): void {
    // 匹配文本内容中的未转义 < 字符
    const textContentRegex = />([^<]*?)<(?![\/\w!?])([^<]*?)</g;
    let match;

    while ((match = textContentRegex.exec(xmlContent)) !== null) {
      const position = match.index + match[1].length + 1; // < 字符的位置
      const line = this.getLineNumber(xmlContent, position);

      this.addError('error', 'UNESCAPED_LESS_THAN',
        `第${line}行: 文本内容中有未转义的 < 字符，应使用 &lt;`, line);
    }
  }

  /**
   * 检查未转义的 > 字符
   */
  private checkUnescapedGreaterThan(xmlContent: string): void {
    // 匹配文本内容中的未转义 > 字符（在不应该出现的地方）
    const invalidGreaterThanRegex = />([^<]*?)>(?![^<]*?<)([^<]*?)</g;
    let match;

    while ((match = invalidGreaterThanRegex.exec(xmlContent)) !== null) {
      // 检查是否是真正的问题（避免误报）
      const beforeContext = match[1];
      const afterContext = match[2];

      // 如果前后都有实际内容，可能是未转义的 >
      if (beforeContext.trim() && afterContext.trim()) {
        const position = match.index + match[1].length + 1; // > 字符的位置
        const line = this.getLineNumber(xmlContent, position);

        this.addWarning('UNESCAPED_GREATER_THAN',
          `第${line}行: 可能有未转义的 > 字符，建议检查是否应使用 &gt;`, line);
      }
    }
  }

  /**
   * 检查JATS合规性
   */
  private checkJATSCompliance(xmlContent: string, xmlDoc: Document): void {
    // 检查DTD声明
    this.checkDTDDeclaration(xmlContent);
    
    // 检查根元素
    this.checkRootElement(xmlDoc);
    
    // 检查命名空间
    this.checkNamespaces(xmlDoc);
  }

  /**
   * 检查DTD声明
   */
  private checkDTDDeclaration(xmlContent: string): void {
    const doctypeMatch = xmlContent.match(/<!DOCTYPE[^>]+>/i);

    if (!doctypeMatch) {
      this.addWarning('DTD_DECLARATION_MISSING', '缺少DTD声明');
      return;
    }

    const doctypeDeclaration = doctypeMatch[0];

    // 检查是否包含JATS相关的DTD标识
    const hasJATSIdentifier = doctypeDeclaration.includes('JATS') ||
                             doctypeDeclaration.includes('Journal Publishing DTD') ||
                             doctypeDeclaration.includes('Journal Archiving') ||
                             doctypeDeclaration.includes('Article Authoring');

    if (!hasJATSIdentifier) {
      this.addWarning('INVALID_JATS_DTD',
        `DTD声明可能不是标准的JATS DTD: ${doctypeDeclaration}`);
    }

    // 检查DTD版本
    if (doctypeDeclaration.includes('v1.2') || doctypeDeclaration.includes('1.2')) {
      this.addInfo('DTD_VERSION_INFO', 'DTD版本: JATS 1.2');
    }
  }

  /**
   * 检查根元素
   */
  private checkRootElement(xmlDoc: Document): void {
    const rootElement = xmlDoc.documentElement;
    
    if (!rootElement) {
      this.addError('error', 'NO_ROOT_ELEMENT', '缺少根元素');
      return;
    }
    
    if (rootElement.tagName !== 'article') {
      this.addError('error', 'INVALID_ROOT_ELEMENT', 
        `根元素应该是 'article'，但找到 '${rootElement.tagName}'`);
    }
    
    // 检查根元素必需属性
    if (!rootElement.hasAttribute('article-type')) {
      this.addError('error', 'MISSING_REQUIRED_ATTRIBUTE', 
        '根元素 article 缺少必需属性 article-type');
    }
  }

  /**
   * 检查命名空间
   */
  private checkNamespaces(xmlDoc: Document): void {
    const rootElement = xmlDoc.documentElement;
    
    // 检查常见的JATS命名空间
    const expectedNamespaces = {
      'xmlns:xlink': 'http://www.w3.org/1999/xlink',
      'xmlns:mml': 'http://www.w3.org/1998/Math/MathML'
    };
    
    Object.entries(expectedNamespaces).forEach(([attr, expectedValue]) => {
      const actualValue = rootElement.getAttribute(attr);
      if (actualValue && actualValue !== expectedValue) {
        this.addWarning('INVALID_NAMESPACE', 
          `命名空间 ${attr} 的值可能不正确: ${actualValue}`);
      }
    });
  }

  /**
   * 检查结构
   */
  private checkStructure(xmlDoc: Document): void {
    this.checkElementStructure(xmlDoc.documentElement, null);
  }

  /**
   * 递归检查元素结构
   */
  private checkElementStructure(element: Element, parent: Element | null): void {
    const tagName = element.tagName;
    const rule = JATS_RULES.elements[tagName];
    
    if (!rule && this.options.strictMode) {
      this.addWarning('UNKNOWN_ELEMENT', `未知的JATS元素: ${tagName}`);
    }
    
    if (rule) {
      // 检查父元素
      if (parent && rule.allowedParents && !rule.allowedParents.includes(parent.tagName)) {
        this.addError('error', 'INVALID_PARENT', 
          `元素 ${tagName} 不能作为 ${parent.tagName} 的子元素`);
      }
      
      // 检查子元素
      const children = Array.from(element.children);
      children.forEach(child => {
        if (rule.allowedChildren && !rule.allowedChildren.includes(child.tagName)) {
          this.addError('error', 'INVALID_CHILD', 
            `元素 ${child.tagName} 不能作为 ${tagName} 的子元素`);
        }
      });
      
      // 检查必需子元素
      if (rule.requiredChildren) {
        rule.requiredChildren.forEach(requiredChild => {
          const hasRequiredChild = children.some(child => child.tagName === requiredChild);
          if (!hasRequiredChild) {
            this.addError('error', 'MISSING_REQUIRED_CHILD', 
              `元素 ${tagName} 缺少必需的子元素 ${requiredChild}`);
          }
        });
      }
    }
    
    // 递归检查子元素
    Array.from(element.children).forEach(child => {
      this.checkElementStructure(child, element);
    });
  }

  /**
   * 检查属性
   */
  private checkAttributes(xmlDoc: Document): void {
    const allElements = xmlDoc.querySelectorAll('*');
    
    allElements.forEach(element => {
      this.checkElementAttributes(element);
    });
  }

  /**
   * 检查单个元素的属性
   */
  private checkElementAttributes(element: Element): void {
    const tagName = element.tagName;
    const rule = JATS_RULES.elements[tagName];
    
    if (!rule) return;
    
    const attributes = Array.from(element.attributes);
    
    // 检查属性是否允许
    attributes.forEach(attr => {
      const attrName = attr.name;
      const isGlobalAttr = JATS_RULES.globalAttributes.includes(attrName);
      const isAllowedAttr = rule.allowedAttributes?.includes(attrName);
      
      if (!isGlobalAttr && !isAllowedAttr && this.options.strictMode) {
        this.addWarning('UNKNOWN_ATTRIBUTE', 
          `元素 ${tagName} 的属性 ${attrName} 可能不被允许`);
      }
      
      // 检查属性值
      this.checkAttributeValue(element, attrName, attr.value);
    });
    
    // 检查必需属性
    if (rule.requiredAttributes) {
      rule.requiredAttributes.forEach(requiredAttr => {
        if (!element.hasAttribute(requiredAttr)) {
          this.addError('error', 'MISSING_REQUIRED_ATTRIBUTE', 
            `元素 ${tagName} 缺少必需属性 ${requiredAttr}`);
        }
      });
    }
  }

  /**
   * 检查属性值
   */
  private checkAttributeValue(_element: Element, attrName: string, attrValue: string): void {
    const attrRule = JATS_RULES.attributes[attrName];
    
    if (!attrRule) return;
    
    // 检查枚举值
    if (attrRule.type === 'enum' && attrRule.enumValues) {
      if (!attrRule.enumValues.includes(attrValue)) {
        this.addError('error', 'INVALID_ATTRIBUTE_VALUE', 
          `属性 ${attrName} 的值 "${attrValue}" 不在允许的值列表中: ${attrRule.enumValues.join(', ')}`);
      }
    }
    
    // 检查模式匹配
    if (attrRule.pattern) {
      const pattern = new RegExp(attrRule.pattern);
      if (!pattern.test(attrValue)) {
        this.addError('error', 'INVALID_ATTRIBUTE_PATTERN', 
          `属性 ${attrName} 的值 "${attrValue}" 不符合要求的模式`);
      }
    }
  }

  /**
   * 辅助方法
   */
  private isSelfClosingTag(tagName: string): boolean {
    const selfClosingTags = ['br', 'hr', 'img', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'];
    return selfClosingTags.includes(tagName.toLowerCase());
  }

  private isSpecialTag(tagName: string): boolean {
    const specialTags = ['!DOCTYPE', '!--', '?xml'];
    return specialTags.some(tag => tagName.startsWith(tag));
  }

  private addError(type: 'error' | 'warning' | 'info', code: string, message: string, line?: number): void {
    const error: ValidationError = { type, code, message, line };
    
    if (type === 'error') {
      this.errors.push(error);
    } else if (type === 'warning') {
      this.warnings.push(error);
    } else {
      this.info.push(error);
    }
    
    // 检查错误数量限制
    if (this.errors.length >= this.options.maxErrors!) {
      throw new Error(`达到最大错误数量限制 (${this.options.maxErrors})`);
    }
  }

  private addWarning(code: string, message: string, line?: number): void {
    this.addError('warning', code, message, line);
  }

  private addInfo(code: string, message: string, line?: number): void {
    this.addError('info', code, message, line);
  }

  private resetErrors(): void {
    this.errors = [];
    this.warnings = [];
    this.info = [];
  }

  private buildResult(startTime: number): ValidationResult {
    const endTime = performance.now();
    
    return {
      isValid: this.errors.length === 0,
      errors: this.errors,
      warnings: this.warnings,
      info: this.info,
      summary: {
        totalErrors: this.errors.length,
        totalWarnings: this.warnings.length,
        totalInfo: this.info.length,
        processingTime: endTime - startTime
      }
    };
  }
}

// 导出便捷函数
export function validateJATSXML(xmlContent: string, options?: ValidatorOptions): ValidationResult {
  const validator = new JATSValidator(options);
  return validator.validate(xmlContent);
}

export function createValidator(options?: ValidatorOptions): JATSValidator {
  return new JATSValidator(options);
}
