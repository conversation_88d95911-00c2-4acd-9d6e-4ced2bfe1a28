# 调试功能说明

## 已实现的调整

### 1. 三种模式样式调整
- **布局**：从垂直布局改为水平布局（一行显示）
- **样式**：使用更紧凑的设计
  - 使用 `flex flex-wrap gap-6` 实现水平排列
  - 使用 `radio-sm` 和 `text-sm` 减小组件尺寸
  - 简化文本描述，保持简洁

### 2. 调试输出功能
在文章处理完成后，会在浏览器控制台输出详细的调试信息：

```
=== 文章关键词和命中率调试信息 ===
文章 1: Machine Learning Applications in Healthcare...
  PMID: 12345678
  个数命中率: 80.0%
  综合命中率: 45.2%
  命中关键词数: 4/5
  文章关键词: machine learning, healthcare, artificial intelligence, deep learning
  关键词匹配详情:
    - machine learning: 100.0% (exact)
    - artificial intelligence: 90.0% (partial)
    - healthcare: 85.0% (exact)
    - deep learning: 75.0% (semantic)
  ---
```

**调试信息包含：**
- 文章标题（截取前50字符）
- PMID
- 个数命中率（百分比）
- 综合命中率（百分比）
- 命中关键词数量/总关键词数量
- 文章自带的关键词列表
- 每个匹配关键词的详细信息（相似率和匹配类型）

### 3. 调试开发设置
- **默认文章数量**：从500篇改为10篇
- **新增选项**：
  - 10篇（调试开发）- 默认选择
  - 50篇（快速测试）- 新增
  - 保留原有的500、1000、2000、5000、9999篇选项

## 使用方法

### 查看调试信息
1. 打开浏览器开发者工具（F12）
2. 切换到 Console 标签页
3. 执行搜索操作
4. 在控制台查看详细的调试输出

### 调试开发流程
1. 使用默认的10篇文章进行快速测试
2. 检查控制台输出，验证关键词匹配逻辑
3. 根据需要调整到50篇进行更全面的测试
4. 最终使用500篇或更多进行生产环境测试

## 调试信息的作用

### 1. 验证关键词匹配算法
- 检查每个关键词的匹配情况
- 验证相似率计算是否准确
- 确认匹配类型（exact/partial/semantic）是否正确

### 2. 优化综合命中率计算
- 对比个数命中率和综合命中率
- 分析不同文章的排序是否合理
- 调整算法参数以获得更好的结果

### 3. 文章质量评估
- 查看文章自带的关键词
- 评估文章与搜索关键词的相关性
- 识别可能的误匹配或遗漏

### 4. 性能优化
- 使用少量文章进行算法测试
- 避免在开发阶段进行大量API请求
- 快速迭代和验证功能改进

## 注意事项

- 调试输出仅在开发环境中使用
- 生产环境建议移除或条件化控制台输出
- 大量文章时调试信息可能很多，注意控制台性能
- 可以根据需要添加更多调试信息或过滤条件
## 最
新更新 (2024-12-28)

### 智能权重搜索优化 v2.0
- ✅ **解决查询过长问题**：实现了自适应策略选择
- ✅ **字段权重策略**：利用PubMed内置的[Title]、[Title/Abstract]、[MeSH Terms]字段权重
- ✅ **分层查询策略**：按关键词重要性分层，避免过多组合
- ✅ **简单查询策略**：兜底策略，确保查询不会过长
- ✅ **POST请求支持**：PubMedService已支持POST请求避免URL长度限制

### 新权重策略对比

#### 旧策略（重复组合）
```javascript
// 问题：查询过长，可能导致API失败
// 3个关键词：14个组合
// 5个关键词：55个组合
// 查询长度：可能超过2000字符
```

#### 新策略（字段权重）
```javascript
// 优势：查询长度可控，利用PubMed内置权重
// 任意数量关键词：固定4个组合
// 查询长度：通常在200-1000字符范围内

// 示例查询结构：
("keyword1"[Title] AND "keyword2"[Title]) OR
("keyword1"[Title/Abstract] AND "keyword2"[Title/Abstract]) OR  
("keyword1"[MeSH Terms] AND "keyword2"[MeSH Terms]) OR
("keyword1" AND "keyword2")
```

### 自适应策略选择
- **字段权重策略**：≤3个关键词且总长度≤100字符
- **分层查询策略**：≤6个关键词且总长度≤200字符  
- **简单查询策略**：其他所有情况

### 性能提升
- 🚀 查询长度减少60-80%
- 🚀 API调用成功率提升
- 🚀 保持相关性排序效果
- 🚀 支持更多关键词输入

### 已解决问题
- ✅ 查询字符串过长导致的GET请求失败
- ✅ 复杂查询的性能问题
- ✅ URL长度限制问题（通过POST请求）

### 测试结果
根据测试，新策略在不同场景下的表现：

1. **简单场景**（3个短关键词）：213字符，使用字段权重策略
2. **中等场景**（5个关键词）：460字符，使用分层查询策略
3. **复杂场景**（8个长关键词）：549字符，使用简单查询策略
4. **极端场景**（10个很长关键词）：983字符，使用简单查询策略

所有测试用例的查询长度都控制在1000字符以内，远低于2000字符的安全阈值。