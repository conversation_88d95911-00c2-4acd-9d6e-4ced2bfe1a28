# JATS 校验规则第二轮修复文档

## 修复的问题

根据您提供的第二轮错误列表，我们修复了以下校验规则问题：

### 1. 元素父子关系修复

#### ✅ `sec` 元素
- **问题**: `sec` 不能作为 `article` 和 `abstract` 的子元素
- **修复**: 在 `sec` 元素的 `allowedParents` 中添加了 `article` 和 `abstract`
- **原因**: 某些JATS变体允许在 `article` 根元素下直接放置 `sec`，以及在 `abstract` 中使用结构化章节

#### ✅ `xref` 元素
- **问题**: `xref` 不能作为 `contrib` 的子元素
- **修复**: 在 `xref` 元素的 `allowedParents` 中添加了 `contrib`
- **原因**: 作者信息中经常需要交叉引用，如引用机构编号

#### ✅ `label` 元素
- **问题**: `label` 不能作为 `aff` 的子元素
- **修复**: 在 `label` 元素的 `allowedParents` 中添加了 `aff`
- **原因**: 机构信息中需要标签来标识编号

#### ✅ `italic` 元素
- **问题**: `italic` 不能作为 `corresp`, `license-p`, `xref` 的子元素
- **修复**: 在 `italic` 元素的 `allowedParents` 中添加了这些元素
- **原因**: 这些元素中经常需要斜体格式化文本

#### ✅ `title` 元素
- **问题**: `title` 不能作为 `kwd-group`, `caption`, `ack` 的子元素
- **修复**: 在 `title` 元素的 `allowedParents` 中添加了这些元素
- **原因**: 这些元素都可以包含标题

### 2. 具体修复内容

#### A. `sec` 元素更新
```typescript
// 修复前
allowedParents: ['body', 'sec', 'back', 'app', 'boxed-text']

// 修复后
allowedParents: ['abstract', 'article', 'body', 'sec', 'back', 'app', 'boxed-text']
```

#### B. `xref` 元素更新
```typescript
// 修复前
allowedParents: ['p', 'td', 'th', 'title', 'caption', ...]

// 修复后
allowedParents: ['contrib', 'p', 'td', 'th', 'title', 'caption', ...]
```

#### C. `label` 元素更新
```typescript
// 修复前
allowedParents: ['sec', 'app', 'boxed-text', 'fig', ...]

// 修复后
allowedParents: ['aff', 'sec', 'app', 'boxed-text', 'fig', ...]
```

#### D. `italic` 元素更新
```typescript
// 修复前
allowedParents: ['p', 'td', 'th', 'title', 'caption', ...]

// 修复后
allowedParents: ['corresp', 'license-p', 'xref', 'p', 'td', 'th', 'title', 'caption', ...]
```

#### E. `title` 元素更新
```typescript
// 修复前
allowedParents: ['sec', 'app', 'boxed-text', 'fig', ...]

// 修复后
allowedParents: ['kwd-group', 'caption', 'ack', 'abstract', 'sec', 'app', 'boxed-text', 'fig', ...]
```

### 3. 修复前后对比

#### 修复前的错误
```
❌ 元素 sec 不能作为 article 的子元素
❌ 元素 xref 不能作为 contrib 的子元素 (多次)
❌ 元素 label 不能作为 aff 的子元素 (多次)
❌ 元素 italic 不能作为 corresp 的子元素
❌ 元素 italic 不能作为 license-p 的子元素
❌ 元素 sec 不能作为 abstract 的子元素 (多次)
❌ 元素 title 不能作为 kwd-group 的子元素
❌ 元素 italic 不能作为 xref 的子元素 (多次)
❌ 元素 title 不能作为 caption 的子元素 (多次)
❌ 元素 title 不能作为 ack 的子元素
```

#### 修复后
✅ 所有上述错误都已修复，相关元素现在都可以正确嵌套

### 4. 校验逻辑说明

校验器使用以下逻辑检查元素关系：

```typescript
// 检查父元素
if (parent && rule.allowedParents && !rule.allowedParents.includes(parent.tagName)) {
  this.addError('error', 'INVALID_PARENT', 
    `元素 ${tagName} 不能作为 ${parent.tagName} 的子元素`);
}
```

这意味着：
- 每个元素的 `allowedParents` 数组定义了它可以出现在哪些父元素中
- 如果当前元素的父元素不在 `allowedParents` 列表中，就会报错

### 5. JATS 标准兼容性

这些修复提高了对不同JATS变体的兼容性：

1. **结构化摘要**: 支持在 `abstract` 中使用 `sec` 元素
2. **灵活的文档结构**: 支持在 `article` 根元素下直接放置 `sec`
3. **丰富的作者信息**: 支持在 `contrib` 中使用 `xref` 进行交叉引用
4. **完整的机构信息**: 支持在 `aff` 中使用 `label` 标识
5. **灵活的格式化**: 支持在更多元素中使用 `italic` 和 `title`

### 6. 测试建议

建议使用以下XML片段测试修复效果：

```xml
<!-- 测试 article 下的 sec -->
<article>
  <front>...</front>
  <sec sec-type="intro">
    <title>Introduction</title>
    <p>Content</p>
  </sec>
</article>

<!-- 测试 contrib 中的 xref -->
<contrib contrib-type="author">
  <name>
    <surname>Smith</surname>
    <given-names>John</given-names>
  </name>
  <xref ref-type="aff" rid="aff1">
    <sup>1</sup>
  </xref>
</contrib>

<!-- 测试 abstract 中的 sec -->
<abstract>
  <sec>
    <title>Background</title>
    <p>Background information</p>
  </sec>
</abstract>

<!-- 测试 aff 中的 label -->
<aff id="aff1">
  <label>1</label>
  <institution>University</institution>
</aff>
```

### 7. 总结

通过这次修复，JATS校验器现在能够：

1. ✅ 正确处理更灵活的文档结构
2. ✅ 支持结构化摘要
3. ✅ 允许丰富的作者和机构信息标记
4. ✅ 支持更多的格式化选项
5. ✅ 提供更准确的校验结果，减少误报

这些改进使得校验器更加符合实际的JATS XML文档使用场景，提高了实用性和准确性。
