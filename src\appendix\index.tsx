import { createSignal } from 'solid-js';

interface AppendixFormData {
  mid: string;
  ppr: boolean;
  rc: boolean;
  tp: boolean;
  dss: boolean;
  prf: boolean;
  authors: string;
}

function removeBorderStyles(element: HTMLElement) {
    // 移除当前元素的 border 样式
    if (element.style) {
      element.style.border = 'none';
      element.style.borderTop = 'none';
      element.style.borderRight = 'none';
      element.style.borderBottom = 'none';
      element.style.borderLeft = 'none';
    }
  
    // 递归处理子元素
    for (let i = 0; i < element.children.length; i++) {
      removeBorderStyles(element.children[i] as HTMLElement);
    }
  }
  

function copyToClipboard(html: string) {
    console.log(html)
  // 创建一个隐藏的可编辑容器
  const container = document.createElement('div');
  container.innerHTML = html;
  container.style.position = 'fixed';
  container.style.pointerEvents = 'none';
  container.style.opacity = '0';
  removeBorderStyles(container);
  document.body.appendChild(container);

  // 选择容器内容
  window.getSelection()?.removeAllRanges();
  const range = document.createRange();
  range.selectNode(container);
  window.getSelection()?.addRange(range);

  try {
    // 执行复制命令
    document.execCommand('copy');
    alert('已复制到剪贴板');
  } catch (err) {
    alert('复制失败');
  } finally {
    // 清理
    window.getSelection()?.removeAllRanges();
    document.body.removeChild(container);
  }
}

function generateAppendix({mid, ppr, rc, tp, dss, prf, authors}: AppendixFormData) {
    mid = mid.toLowerCase()
    let m = mid.split("-")
    let domain = m[0]
    if (domain.toLowerCase() === "acs") {
        domain = "https://www.annalscts.com"
    }else if (domain.toLowerCase() === "ajo") {
        domain = "https://www.theajo.com"
    } else {
        domain = `https://${domain.toLowerCase()}.amegroups.com`
    }

    let RCauthorStr = authors === "1" ? "The author has" : "The authors have"
    let COIauthorStr = authors === "1" ? "The author has" : authors === "2" ? "Both authors have" : "All authors have"
    let ESauthorStr = authors === "1" ? "The author is" : "The authors are"

    let pprstr = ppr ?  `<br>Provenance and Peer Review: This article was commissioned by the editorial office, <strong>xxx</strong>. <strong>The article has undergone external peer review</strong>.` :"";
    
    let rcstr = rc ?  `<br><br>${authors === "1" ? "This article is presented" : "We present this article"} in accordance with the <strong>xxxx</strong> reporting checklist (available at <a href="${domain}/article/view/10.21037/${mid}/rc" class="text-blue-600 hover:underline">${domain}/article/view/10.21037/${mid}/rc</a>).` :"";
    
    let aiStr = rc ? `<br><br>Article information: <a href="https://dx.doi.org/10.21037/${mid}" class="text-blue-600 hover:underline">https://dx.doi.org/10.21037/${mid}</a>
*As the checklist was provided upon initial submission, the page number/line number reported may be changed due to copyediting and may not be referable in the published version. In this case, the section/paragraph may be used as an alternative reference.<br>` : ""

    let rc2str = rc ?  `<br>Reporting Checklist: ${RCauthorStr} completed the <strong>xxxx</strong> reporting checklist. Available at <a href="${domain}/article/view/10.21037/${mid}/rc" class="text-blue-600 hover:underline">${domain}/article/view/10.21037/${mid}/rc</a>` :"";
    
    let tpstr = tp ?  `<br>Trial Protocol: Available at <a href="${domain}/article/view/10.21037/${mid}/tp" class="text-blue-600 hover:underline">${domain}/article/view/10.21037/${mid}/tp</a>` :"";

    let dssstr = dss ?  `<br>Data Sharing Statement: Available at <a href="${domain}/article/view/10.21037/${mid}/dss" class="text-blue-600 hover:underline">${domain}/article/view/10.21037/${mid}/dss</a>` :"";

    let prfstr = prf ?  `<br>Peer Review File: Available at <a href="${domain}/article/view/10.21037/${mid}/prf" class="text-blue-600 hover:underline">${domain}/article/view/10.21037/${mid}/prf</a>` :"";

    let coistr = `<br><br>Conflicts of Interest: ${COIauthorStr} completed the ICMJE uniform disclosure form (available at <a href="${domain}/article/view/10.21037/${mid}/coif" class="text-blue-600 hover:underline">${domain}/article/view/10.21037/${mid}/coif</a>). <strong>${authors ==="1" ? "The author has" : "The authors have"} no conflicts of interest to declare.</strong>`
    
    const displayContent = `<div class="font-serif whitespace-pre-line">
    ${rc ? `<p class="font-bold">${mid.toLocaleUpperCase()}-Reporting Checklist</p>` : ""}${tp ? `<p class="font-bold">${mid.toLocaleUpperCase()}-Trial Protocol</p>` : ""}${dss ? `<p class="font-bold">${mid.toLocaleUpperCase()}-Data Sharing Statement</p>` : ""}${prf ? `<p class="font-bold">${mid.toLocaleUpperCase()}-Peer Review File</p>` : ""}<p class="font-bold">${mid.toLocaleUpperCase()}-COI Form</p>
<br>doi: 10.21037/${mid}
<br>View this article at: <a href="https://dx.doi.org/10.21037/${mid}" class="text-blue-600 hover:underline">https://dx.doi.org/10.21037/${mid}</a>${rcstr}${aiStr}
<p class="font-bold">Acknowledgments</p>
<strong>xxx</strong>

<p class="font-bold">Footnote</p>${pprstr}${rc2str}${tpstr}${dssstr}${prfstr}
Funding: <strong>xxx</strong>.${coistr}

Ethical Statement: ${ESauthorStr} accountable for all aspects of the work in ensuring that questions related to the accuracy or integrity of any part of the work are appropriately investigated and resolved. <strong>xxx</strong>.</div>`

    const copyContent = `${rc ? `${mid.toLocaleUpperCase()}-Reporting Checklist<br>` : ""}${tp ? `${mid.toLocaleUpperCase()}-Trial Protocol<br>` : ""}${dss ? `${mid.toLocaleUpperCase()}-Data Sharing Statement<br>` : ""}${prf ? `${mid.toLocaleUpperCase()}-Peer Review File<br>` : ""}${mid.toLocaleUpperCase()}-COI Form

doi: 10.21037/${mid}
View this article at: <a href="https://dx.doi.org/10.21037/${mid} class="text-blue-600 hover:underline"">https://dx.doi.org/10.21037/${mid}</a>${rcstr}${aiStr}
Acknowledgments
<strong>xxx</strong>

Footnote
${pprstr}${rc2str}${tpstr}${dssstr}${prfstr}
Funding: <strong>xxx</strong>.${coistr}

Ethical Statement: ${ESauthorStr} accountable for all aspects of the work in ensuring that questions related to the accuracy or integrity of any part of the work are appropriately investigated and resolved. <strong>xxx</strong>.`

    return {displayContent, copyContent};
}

export default function AppendixGenerator() {
  const [formData, setFormData] = createSignal<AppendixFormData>({
    mid: '',
    ppr: false,
    rc: false,
    tp: false,
    dss: false,
    prf: false,
    authors: '1'
  });

  const [result, setResult] = createSignal<{displayContent: string, copyContent: string} | null>(null);

  const handleSubmit = (e: Event) => {
    e.preventDefault();
    const appendixContent = generateAppendix(formData());
    setResult(appendixContent);
  };

  const handleCopy = () => {
    if (result()) {
      copyToClipboard(result()!.displayContent);
    }
  };

  return (
    <div class="p-4">
      <h1 class="text-2xl font-bold mb-4">文章附录生成</h1>
      
      <form onSubmit={handleSubmit} class="space-y-4 mb-8">
        <div class="form-control">
          <label class="label">
            <span class="label-text">文章ID (MID)</span>
          </label>
          <input
            type="text"
            class="input input-bordered w-full max-w-xs"
            value={formData().mid}
            onInput={(e) => setFormData({...formData(), mid: e.currentTarget.value})}
            required
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">作者数量</span>
          </label>
          <select 
            class="select select-bordered w-full max-w-xs"
            value={formData().authors}
            onChange={(e) => setFormData({...formData(), authors: e.currentTarget.value})}
          >
            <option value="1">1位作者</option>
            <option value="2">2位作者</option>
            <option value="3">多位作者</option>
          </select>
        </div>

        <div class="space-y-2">
          <label class="flex items-center gap-2">
            <input
              type="checkbox"
              class="checkbox"
              checked={formData().ppr}
              onChange={(e) => setFormData({...formData(), ppr: e.currentTarget.checked})}
            />
            <span>Provenance and Peer Review</span>
          </label>

          <label class="flex items-center gap-2">
            <input
              type="checkbox"
              class="checkbox"
              checked={formData().rc}
              onChange={(e) => setFormData({...formData(), rc: e.currentTarget.checked})}
            />
            <span>Reporting Checklist</span>
          </label>

          <label class="flex items-center gap-2">
            <input
              type="checkbox"
              class="checkbox"
              checked={formData().tp}
              onChange={(e) => setFormData({...formData(), tp: e.currentTarget.checked})}
            />
            <span>Trial Protocol</span>
          </label>

          <label class="flex items-center gap-2">
            <input
              type="checkbox"
              class="checkbox"
              checked={formData().dss}
              onChange={(e) => setFormData({...formData(), dss: e.currentTarget.checked})}
            />
            <span>Data Sharing Statement</span>
          </label>

          <label class="flex items-center gap-2">
            <input
              type="checkbox"
              class="checkbox"
              checked={formData().prf}
              onChange={(e) => setFormData({...formData(), prf: e.currentTarget.checked})}
            />
            <span>Peer Review File</span>
          </label>
        </div>

        <button type="submit" class="btn btn-primary">生成附录</button>
      </form>

      {result() && (
        <div class="mt-8">
          <div class="flex justify-between items-center mb-2">
            <h2 class="text-xl font-bold">生成结果</h2>
            <button onClick={handleCopy} class="btn btn-sm btn-outline">
              复制内容
            </button>
          </div>
          <div 
            class="p-4 bg-base-200 rounded-lg leading-relaxed"
            innerHTML={result()!.displayContent}
          />
        </div>
      )}
    </div>
  );
}