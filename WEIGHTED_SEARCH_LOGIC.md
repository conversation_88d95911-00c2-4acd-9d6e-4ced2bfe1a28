# PubMed 智能权重搜索逻辑

## 概述
本文档描述了PubMed搜索系统中使用的智能权重搜索策略，旨在通过优化查询组合来提高搜索结果的相关性和准确性，同时避免查询过长导致的API调用失败。

## 新实现的权重策略

### 1. 自适应策略选择

系统根据关键词数量和总长度自动选择最佳查询策略：

```javascript
// 策略选择逻辑
const totalLength = keywordList.join('').length;
const keywordCount = keywordList.length;

if (keywordCount <= 3 && totalLength <= 100) {
  strategy = 'field_weighted';  // 字段权重策略
} else if (keywordCount <= 6 && totalLength <= 200) {
  strategy = 'layered';         // 分层查询策略
} else {
  strategy = 'simple';          // 简单查询策略
}
```

### 2. 字段权重策略（field_weighted）

利用PubMed的内置字段权重机制，不依赖重复查询：

```javascript
// 示例：关键词 ["machine learning", "AI", "neural networks"]
// 生成的查询：

// 最高权重：标题中的匹配
("machine learning"[Title] AND "AI"[Title] AND "neural networks"[Title])

OR

// 高权重：标题或摘要中的匹配  
("machine learning"[Title/Abstract] AND "AI"[Title/Abstract] AND "neural networks"[Title/Abstract])

OR

// 中等权重：MeSH主题词中的匹配
("machine learning"[MeSH Terms] AND "AI"[MeSH Terms] AND "neural networks"[MeSH Terms])

OR

// 较低权重：任意字段中的匹配
("machine learning" AND "AI" AND "neural networks")
```

#### 优势
- 利用PubMed的内置权重机制
- 查询长度可控，不会过长
- 标题匹配获得最高权重，符合相关性排序

### 3. 分层查询策略（layered）

按关键词重要性分层，适用于中等数量的关键词：

```javascript
// 第一层：最重要的前3个关键词（标题/摘要匹配）
("keyword1"[Title/Abstract] AND "keyword2"[Title/Abstract] AND "keyword3"[Title/Abstract])

OR

// 第二层：前5个关键词（任意字段匹配）
("keyword1" AND "keyword2" AND "keyword3" AND "keyword4" AND "keyword5")

OR

// 第三层：递减组合（避免过多组合）
("keyword1" AND "keyword2" AND "keyword3" AND "keyword4")
("keyword1" AND "keyword2" AND "keyword3")
("keyword1" AND "keyword2")
```

#### 优势
- 平衡了查询精度和覆盖范围
- 避免生成过多组合
- 保持查询长度在合理范围内

### 4. 简单查询策略（simple）

当关键词过多或过长时使用的降级策略：

```javascript
// 只使用最重要的5个关键词
// 标题或摘要匹配
("keyword1"[Title/Abstract] AND "keyword2"[Title/Abstract] AND ... AND "keyword5"[Title/Abstract])

OR

// 任意字段匹配
("keyword1" AND "keyword2" AND ... AND "keyword5")

OR

// 部分关键词组合
("keyword1" AND "keyword2" AND "keyword3" AND "keyword4")
```

#### 优势
- 确保查询不会过长
- 保持基本的相关性排序
- 适用于所有情况的兜底策略

## 技术实现细节

### 查询长度控制
```javascript
// 查询长度检测和降级
const query = generateWeightedQuery(keywordList, strategy);

if (query.length > 2000) {
  console.log('查询过长，降级到简单策略');
  return generateWeightedQuery(keywordList, 'simple');
}
```

### PubMed API 优化
- 自动检测查询长度，选择GET或POST请求
- POST请求避免URL长度限制
- 支持分批处理大量结果

```javascript
// PubMedService 中的实现
const queryLength = params.query.length;
const usePost = queryLength > 2000;

if (usePost) {
  // 使用POST请求
  response = await fetch(this.SEARCH_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: formData.toString()
  });
}
```

## 性能对比

### 旧策略（重复组合）
- 3个关键词：14个组合
- 5个关键词：55个组合  
- 10个关键词：385个组合
- 查询长度：可能超过URL限制

### 新策略（字段权重）
- 任意数量关键词：固定4个组合
- 查询长度：可控且合理
- 权重机制：利用PubMed内置机制

## 实际应用效果

### 查询示例
输入关键词：`machine learning, artificial intelligence, deep learning`

**字段权重策略生成的查询：**
```
("machine learning"[Title] AND "artificial intelligence"[Title] AND "deep learning"[Title]) OR 
("machine learning"[Title/Abstract] AND "artificial intelligence"[Title/Abstract] AND "deep learning"[Title/Abstract]) OR 
("machine learning"[MeSH Terms] AND "artificial intelligence"[MeSH Terms] AND "deep learning"[MeSH Terms]) OR 
("machine learning" AND "artificial intelligence" AND "deep learning")
```

**查询长度：** 约400字符（vs 旧策略的2000+字符）

## 优势总结

1. **避免查询过长**：不再依赖重复组合，查询长度可控
2. **利用内置权重**：使用PubMed的字段权重机制，更符合搜索引擎设计
3. **自适应选择**：根据输入自动选择最佳策略
4. **性能优化**：减少不必要的复杂查询，提高API调用成功率
5. **保持相关性**：仍然确保最相关的结果获得最高权重

## 未来改进方向

1. **动态权重调整**：根据搜索结果质量动态调整策略
2. **关键词重要性分析**：智能识别关键词的重要性排序
3. **领域特定优化**：针对不同学科领域优化搜索策略