chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "inject") {
        injectScript(request.script);
        sendResponse({ status: "script injected" });
    }
});

function injectScript(script) {
    const scriptElement = document.createElement("script");
    scriptElement.textContent = script;
    (document.head || document.documentElement).appendChild(scriptElement);
    scriptElement.remove();
}

function injectStyle(css) {
    const styleElement = document.createElement("style");
    styleElement.textContent = css;
    (document.head || document.documentElement).appendChild(styleElement);
    styleElement.remove();
}

injectStyle(`
    body {
        background-color: #f0f0f0;
        font-family: Arial, sans-serif;
    }
`);