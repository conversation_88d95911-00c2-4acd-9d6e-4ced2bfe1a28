# XML批量修改工具 - 目录整理功能

## 概述

本工具已更新，支持两种目录整理模式，符合JATS XML文件包装规范。

## 功能特性

### 两种目录整理模式

### 两种发布模式（仅结构化模式）

#### 1. 平铺模式 (Flat Structure)
- **描述**: 所有文件保持在同一目录下，仅进行重命名操作
- **适用场景**: 简单的文件重命名需求，保持原有目录结构
- **操作**: 
  - XML文件：格式转换 + 重命名
  - 附件文件：重命名（基于Publisher ID匹配）

#### 2. 结构化模式 (Structured Package)
- **描述**: 按JATS标准创建结构化目录包
- **适用场景**: 需要符合JATS文件包装规范的发布流程

#### 1. PAP模式
- **描述**: 不创建issue.xml文件
- **目录结构**:
  ```
  根目录/
  ├── manifest.xml            # 所有文章的文件清单
  └── articles/              # 文章目录
      ├── article-001/        # 文章1目录
      │   ├── article-001.xml
      │   ├── article-001.pdf
      │   └── article-001_figure1.jpg
      └── article-002/        # 文章2目录
          ├── article-002.xml
          ├── article-002.pdf
          └── article-002_video.mp4
  ```

#### 2. Continuous Publication模式
- **描述**: 创建issue目录和issue.xml，移动cover文件到issue目录
- **目录结构**:
  ```
  根目录/
  ├── manifest.xml            # 所有文章的文件清单
  ├── issue/                 # 期刊目录
  │   ├── issue.xml          # 期刊信息
  │   └── cover.jpg          # 封面文件
  └── articles/              # 文章目录
      ├── article-001/        # 文章1目录
      │   ├── article-001.xml
      │   ├── article-001.pdf
      │   └── article-001_figure1.jpg
      └── article-002/        # 文章2目录
          ├── article-002.xml
          ├── article-002.pdf
          └── article-002_video.mp4
  ```

### 自动生成的文件

#### manifest.xml
包含所有文章的文件清单信息：
- 包ID和创建时间
- 文章数量
- 每个文章的ID、Publisher ID和文件列表

#### issue.xml
包含期刊发布信息：
- 期刊卷期信息
- 发布日期
- 所有文章列表

## 文件分类规则

### 结构化模式
- 为每个文章创建独立的子目录：`articles/article-id/`
- 每个文章的所有文件（XML和附件）都放在其专属目录下
- 根目录包含 `manifest.xml`
- Continuous Publication模式下额外创建 `issue/` 目录和 `issue.xml`
- cover文件在Continuous Publication模式下移动到 `issue/` 目录

## 使用方法

1. **选择目录整理模式**
   - 平铺模式：适合简单重命名
   - 结构化模式：适合JATS标准发布

2. **选择包含XML文件的目录**
   - 工具会自动扫描所有支持的文件类型

3. **开始批量处理**
   - 系统会为每个XML文件提取Publisher ID和新文件名
   - 根据选择的模式进行文件处理和目录整理

## 技术实现

### 核心功能
- `createDirectoryStructure()`: 创建JATS标准目录结构
- `createManifestXml()`: 生成文件清单
- `createIssueXml()`: 生成期刊信息
- `getFileCategory()`: 文件类型分类
- `renameOrMoveFile()`: 文件重命名和移动

### 处理流程
1. 提取XML文件信息（Publisher ID, DOI文件名）
2. 创建目录结构（结构化模式）
3. 处理XML文件（格式转换 + 重命名/移动）
4. 处理相关附件文件（重命名/移动到对应目录）
5. 生成manifest.xml和issue.xml（结构化模式）

## 支持的文件格式

- **XML**: `.xml`
- **视频**: `.mp4`, `.avi`, `.mov`
- **图片**: `.jpg`, `.jpeg`, `.png`
- **文档**: `.pdf`, `.doc`, `.docx`, `.txt`
- **压缩包**: `.zip`

## 注意事项

1. **文件安全**: 所有操作直接在本地文件系统进行，确保数据安全
2. **错误处理**: 完善的错误处理和回滚机制
3. **进度跟踪**: 实时显示处理进度和状态
4. **兼容性**: 支持现代浏览器的File System Access API

## 更新日志

### v2.0.0
- ✅ 新增结构化目录整理模式
- ✅ 自动生成manifest.xml和issue.xml
- ✅ 智能文件分类和目录组织
- ✅ 改进的用户界面和进度显示
- ✅ 完善的错误处理和状态反馈
