import JSZip from "jszip";
import commonImpl from "./doc/common";
import qimsImpl from "./doc/qimsRefList";
import zhRefImpl from "./doc/zhRefList";
import cjtsRefImpl from "./doc/cjtsRefList";
import zhArticleRefImpl from "./doc/zhArticleRefList";
import {Api, RefListApi} from "../restful/api/restApi";
import Mammonth from "mammoth";
import {IContentId} from "./doc/common/model";

interface IDocFile {
    fileName: string,
    originContent: string,
    docContent: string [],
    xmlDoc: string[],
    refIdList: string[]
}


export default class ParseDocFile {
    public zip = null;
    private selectFiles = null;
    private select: number
    private timerId: NodeJS.Timeout
    private idList: IContentId[]
    private selector = null
    private current: HTMLElement
    private fileArr: IDocFile[]
    private callback: Function
    private len: number

    constructor(select, selectFiles, selector, callback) {
        this.zip = new JSZip()
        this.select = select
        this.selectFiles = selectFiles
        this.selector = selector
        this.current = document.getElementById("current")
        this.callback = callback
        this.fileArr = []
        this.idList = []
        this.len = 3
    }
    async readFiles() {
        this.zip = new JSZip()
        let contentList = []
        await this.readFileRefIds(this.selectFiles).then((res: any) => {
            res.forEach(item => {
                this.fileArr.push(item)
                contentList = contentList.concat(item.docContent)
            })
        })
        this.handleRequestId(contentList)
    }

    async readFileRefIds(files) {
        let promises = []
        Array.prototype.forEach.call(files, (file, key) => {
            let p = this.ReadDoc(file).then(async (res: ArrayBuffer) => {
                let name = file.name.replace(/\.(doc|docx)/i, '')
                try {
                    let {originContent, content} = await this.parseDoc(res)
                    return Promise.resolve({
                        fileName: name,
                        originContent,
                        docContent: content,
                        xmlDoc: [],
                        refIdList: []
                    })
                } catch (e) {
                    return Promise.resolve({
                        fileName: name,
                        originContent: null,
                        docContent: [],
                        xmlDoc: [],
                        refIdList: []
                    })
                }
            })
            promises.push(p)
        })
        return Promise.all(promises)
    }


    async ReadDoc(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = (event) => {
                resolve(reader.result)
            }
            reader.readAsArrayBuffer(file)
        })
    }


   parseDoc(binaryStr: ArrayBuffer): Promise<{ originContent: string, content: string[] }> {
       return Mammonth.convertToHtml({ arrayBuffer: binaryStr }).then(res => {
           return {
               originContent: res.value,
               content: this.getDocRefList(res.value)
           };
       });
   }

    getDocRefList(content: string): string [] {
        let splitTag = content.trim().indexOf('<li>') != -1 ? /<\/li><li>/ : /<\/p><p>/
        content = content.replace(/(<p>)*(<strong>)*References?(<\/strong>)*(<br \/>)*(<br>)*\s*/, '')
        content = content.replace(/(<\/p>)*(<ol>|<ul>)/, '')
        let result = content.split(splitTag).filter(item => item)
        //result 为空的处理
        result = result?.map(item => {
            return item
                .replace(/^(<p>|<li>)*[\[|\(]*[\d]+[\]|\)]*(\s)*[.、]*(\s)*/, '')
                .replace(/<li>/, "")
                // .replace(/<a.*>.*<\/a>/g, '')
                .replace(/<\/p>/, '')
                .replace(/<\/li>(<\/ol>|<\/ul>)/, '')
                .trimEnd()
        })
        return result
    }

    async handleRequestId(searchQueue) {
        let circleSum = Math.floor(searchQueue.length / this.len)
        circleSum += searchQueue.length % this.len ? 1 : 0
        let array = Array.from({length: circleSum}, (x, i) => i)
        for (let key of array) {
            let tempQueue = searchQueue.splice(0, this.len)
            await this.startTask(tempQueue).then((res: IContentId []) => {
                for (let obj of res) {
                    this.idList.push(obj)
                }
            })
            await this.sleep(1000)
        }
        this.handleSummary().then(res => {
            this.callback(res, this.zip)
        })
    }


    async handleSummary() {
        let pmids = []
        pmids = this.idList.map(item => item.id).filter(id => id > -1)
        if (!pmids || !pmids.length) {
            return null
        }
        let refListImpl = null
        let promises = []
        let fetchData:Object = await this.getFetch(pmids.join(","))
        await this.getSummary(pmids.join(",")).then((res: any) => {
            for (let pmid in fetchData) {
                if (!res.result[pmid]["pages"]){
                    res.result[pmid]["pii"]=fetchData[pmid]['pii']
                }
            }
            this.fileArr.forEach((item, key) => {
                let p = null
                if (this.selectFiles.length > 1) {
                    let opt = document.createElement('option')
                    opt.value = `${key}`
                    opt.innerHTML = item.fileName
                    this.selector.appendChild(opt)
                }
                let fileName = item.fileName.replace(/\.(doc|docx)/i, "")
                try {
                    if (this.select === 5) {
                        refListImpl = new commonImpl(item.fileName, key, item.docContent, res, this.idList)
                    } else if (this.select === 6) {
                        refListImpl = new qimsImpl(item.fileName, key, item.docContent, res, this.idList)
                    } else if (this.select === 7) {//中文杂志
                        refListImpl = new zhRefImpl(item.fileName, key, item.docContent, res, this.idList)
                    } else if (this.select === 8) {
                        refListImpl = new cjtsRefImpl(item.fileName, key, item.docContent, res, this.idList)
                    } else if (this.select === 9) {//中文书
                        refListImpl = new zhArticleRefImpl(item.fileName, key, item.docContent, res, this.idList)
                    }
                    this.current.innerHTML = `${key + 1}`
                    p = refListImpl.render().then(async html => {
                        if (key === 0) {
                            this.changeFileSelector(key)
                        }
                        html = this.combineHTMLContent(html);
                        let url = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(html);
                        let blob = await fetch(url).then(r => r.blob());
                        this.zip.file(fileName + ".doc", blob)
                        if (this.selectFiles.length === 1) {
                            return Promise.resolve(html)
                        } else {
                            return Promise.resolve(this.zip)
                        }
                    }, rej => {
                        this.zip.file(fileName + ".txt", rej)
                        return Promise.resolve({Error: rej, item})
                    })
                    // this.compareLastId(fileName, key, item.originContent, item.docContent)
                } catch (e) {
                    this.zip.file(fileName + ".txt", e.toString())
                    return Promise.resolve({Error: e, item})
                }
                promises.push(p)
            })
        })
        return Promise.all(promises)
    }

    compareLastId(fileName: string, key: number, originContent: string, docContent: string[]) {
        let separator = ''
        let dLen = docContent.length
        let len = 0
        if (originContent.indexOf('<ul>') != -1 && originContent.indexOf('<li>') != -1) {
            separator = '</li>'
            len = originContent.split('</li>').length - 1
        } else if (originContent.indexOf('<ol>') != -1 && originContent.indexOf('<li>') != -1) {
            separator = '</li>'
            len = originContent.split('</li>').length - 1
        } else {//p标签
            let content = originContent.replace(/(<p>)*(<strong>)*References(<\/strong>)*(<br \/>)*(<br>)*\s*<\/p>/, '')
            if (content.match(/<p>\s*(\[|\)|【|（)\d+(\]|\)|】|）)/)) {
                len = content.replace(/<p>\s*(\[|\)|【|（)\d+(\]|\)|】|）)/g, '<p>()').split('<p>()').length - 1
            } else {
                len = content.split('</p>').length - 1
            }
        }
        if (dLen !== len) {
            // console.log(fileName + ' 最后编号不一致: len= ' + len + ', dLen= ' + dLen)
            let elem = document.getElementById('compareLastId_block')
            let uElem = document.createElement('ol')
            if (!elem) {
                elem = document.createElement('div')
                elem.id = 'compareLastId_block'
                elem.innerText = '最后一行编号不一致的文件列表:'
                elem.appendChild(uElem)
                let vElem = document.getElementById('view1')
                vElem.insertBefore(elem, vElem.children[0])
            }
            let lElem = document.createElement('li')
            lElem.innerHTML = fileName
            uElem.appendChild(lElem)
        }
    }

    async startTask(queue) {
        let array = []
        for (let q of queue) {
            array.push(this.handleRequestContentId(q))
        }
        return await Promise.all(array)
    }

    async sleep(time) {
        await setTimeout(() => {
        }, time)
    }

    handleRequestContentId(content) {
        let regxResult = content.match(/doi:.*\/.*PMID:.*$/g)
        if (regxResult) {
            let doi = regxResult[0]?.replace(/PMID:.*/, '')?.slice(4)?.trim()
            return this.getContentIdByAll(content, doi)
        } else if (content.match(/[\u4E00-\u9F45]+/)) {//包含有中文字符的
            return {
                content,
                id: -2
            }
        }
        return this.getContentIdByAll(content, null)
    }

    getContentIdByAll(content, doi) {
        let flag = ''
        let tempContent = content.replace(/\[J\]/, '')
        flag = content.length >= tempContent.length + 1 ? '[J]' : ''
        let params = {
            term: doi ? `doi:${doi}` : tempContent
        }
        let suffixUrl = new URLSearchParams()
        suffixUrl.append("term", params.term)
        let url = suffixUrl.toString()
        return RefListApi.get(`?${url}`, null, null).then(res => {
            let id = res.pmid?.replace(/\//g, "") || 0
            return {
                content,
                id: id,
                flag
            }
        }).catch(err => {
            console.warn(err)
            return {
                content,
                id: -2
            }
        })
    }


    objectToQueryString(obj: object, modify?: boolean) {
        let str = [];
        for (let p in obj)
            if (obj.hasOwnProperty(p)) {
                str.push(p + "=" + obj[p]);
            }
        if (modify) {
            return `${str.join("&")}`;
        } else if (Object.keys(obj).length) {
            return `?${str.join("&")}`
        } else {
            return ""
        }
    };


    getSummary(ids): Promise<string> {
        return Api.get("/entrez/eutils/esummary.fcgi", {
            db: "pubmed",
            id: ids,
            retmode: 'json'
        }, {}).then(res => {
            return res
        })
    }

    getFetch(ids): Promise<string> {
        return Api.get("/entrez/eutils/efetch.fcgi", {
            db: "pubmed",
            id: ids
        }, {}).then(res => {
            return this.convertXMLToJson(res)
        })
    }

    convertXMLToJson(xml: string) {
        let xmldom = new DOMParser().parseFromString(xml, "text/xml")
        let pubmedArticleList = xmldom.getElementsByTagName("PubmedArticle")
        let data:Object={}
        Array.prototype.forEach.call(pubmedArticleList,(article:HTMLElement) =>{
            let pmid = article.querySelector("PMID")?.textContent
            data[pmid]={
                pii:article.querySelector("Article ELocationID[EIdType='pii']")?.textContent
            }
        })
        return data
    }

    combineHTMLContent(html) {
        let preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";
        let postHtml = "</body></html>";
        return preHtml + html + postHtml;
    }

    changeFileSelector(value) {
        let fileDom = document.querySelectorAll('#view1 .file_segment')
        Array.prototype.forEach.call(fileDom, (item) => {
            item.className = 'file_segment'
        })
        let currentDom = document.getElementById('File_' + value)
        currentDom.className = 'file_segment active'
    }
}
