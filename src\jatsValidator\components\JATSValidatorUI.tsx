import { createSignal, createMemo, For, Show } from 'solid-js';
import { jatsValidator, ValidationResult, ValidationError } from '../index';
import { JATSAutoFixer, FixResult } from '../autoFixer';
import { demoTests } from '../test/demoTest';
import { demoXMLTests } from '../test/demoXMLTest';
import { tagClosingTests } from '../test/tagClosingTest';

export default function JATSValidatorUI() {
  const [xmlContent, setXmlContent] = createSignal('');
  const [validationResult, setValidationResult] = createSignal<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = createSignal(false);
  const [selectedFile, setSelectedFile] = createSignal<File | null>(null);
  const [strictMode, setStrictMode] = createSignal(false);
  const [activeTab, setActiveTab] = createSignal<'input' | 'file'>('input');
  const [autoFormat, setAutoFormat] = createSignal(true);
  const [fixResult, setFixResult] = createSignal<FixResult | null>(null);
  const [isFixing, setIsFixing] = createSignal(false);

  // 示例XML内容
  const exampleXML = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd">
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article" dtd-version="1.2" xml:lang="en">
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">example</journal-id>
      <journal-title-group>
        <journal-title>Example Journal</journal-title>
      </journal-title-group>
      <issn pub-type="ppub">1234-5678</issn>
      <publisher>
        <publisher-name>Example Publisher</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="publisher-id">example-001</article-id>
      <article-id pub-id-type="doi">10.1234/example.001</article-id>
      <title-group>
        <article-title>Example Article Title</article-title>
      </title-group>
      <contrib-group>
        <contrib contrib-type="author">
          <name>
            <surname>Smith</surname>
            <given-names>John</given-names>
          </name>
        </contrib>
      </contrib-group>
      <abstract>
        <p>This is an example abstract.</p>
      </abstract>
      <kwd-group>
        <kwd>example</kwd>
        <kwd>JATS</kwd>
      </kwd-group>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Introduction</title>
      <p>This is the introduction section.</p>
    </sec>
  </body>
</article>`;

  // 格式化XML
  const formatXML = () => {
    const content = xmlContent();
    if (!content.trim()) {
      alert('请输入XML内容');
      return;
    }

    try {
      const validator = jatsValidator.createValidator();
      const formatted = validator.formatXML(content);
      setXmlContent(formatted);
    } catch (error) {
      console.error('格式化过程中发生错误:', error);
      alert('格式化过程中发生错误，请检查XML格式');
    }
  };

  // 校验XML
  const validateXML = async () => {
    const content = xmlContent();
    if (!content.trim()) {
      alert('请输入XML内容或上传文件');
      return;
    }

    setIsValidating(true);

    try {
      // 使用setTimeout来避免阻塞UI
      setTimeout(() => {
        let processedContent = content;

        // 如果启用自动格式化，先格式化XML
        if (autoFormat()) {
          try {
            const validator = jatsValidator.createValidator();
            processedContent = validator.formatXML(content);
            // 更新显示的内容为格式化后的内容
            setXmlContent(processedContent);
          } catch (error) {
            console.warn('自动格式化失败，使用原始内容进行校验:', error);
          }
        }

        const result = jatsValidator.validate(processedContent, {
          strictMode: strictMode(),
          checkWellFormedness: true,
          checkJATSCompliance: true,
          checkStructure: true,
          checkAttributes: true
        });

        setValidationResult(result);
        setIsValidating(false);
      }, 100);
    } catch (error) {
      console.error('校验过程中发生错误:', error);
      setIsValidating(false);
      alert('校验过程中发生错误，请检查XML格式');
    }
  };

  // 处理文件上传
  const handleFileUpload = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setXmlContent(content);
      };
      reader.readAsText(file);
    }
  };

  // 清空内容
  const clearContent = () => {
    setXmlContent('');
    setValidationResult(null);
    setSelectedFile(null);
    setFixResult(null);
  };

  // 加载示例
  const loadExample = () => {
    setXmlContent(exampleXML);
    setValidationResult(null);
    setFixResult(null);
  };

  // 加载demo.xml示例
  const loadDemoExample = () => {
    setXmlContent(demoTests.demoXMLContent);
    setValidationResult(null);
    setFixResult(null);
  };

  // 加载有问题的XML示例
  const loadProblematicExample = () => {
    setXmlContent(demoTests.problematicXMLContent);
    setValidationResult(null);
    setFixResult(null);
  };

  // 加载标签闭合测试示例
  const loadTagClosingExample = () => {
    setXmlContent(tagClosingTests.sameLineTagsTest);
    setValidationResult(null);
    setFixResult(null);
  };

  // 生成标准JATS模板
  const loadStandardTemplate = () => {
    const template = demoXMLTests.generateStandardJATSTemplate();
    setXmlContent(template);
    setValidationResult(null);
    setFixResult(null);
  };

  // 测试demo.xml标准合规性
  const testDemoCompliance = () => {
    const content = xmlContent();
    if (!content.trim()) {
      alert('请输入XML内容');
      return;
    }

    const compliance = demoXMLTests.validateAgainstDemoStandard(content);

    if (compliance.isCompliant) {
      alert('✅ XML符合demo.xml标准！');
    } else {
      let message = '❌ XML不符合demo.xml标准:\n\n';
      message += '问题:\n';
      compliance.issues.forEach((issue, index) => {
        message += `${index + 1}. ${issue}\n`;
      });
      message += '\n建议:\n';
      compliance.suggestions.forEach((suggestion, index) => {
        message += `${index + 1}. ${suggestion}\n`;
      });
      alert(message);
    }
  };

  // 运行标签闭合测试
  const runTagClosingTests = () => {
    console.clear();
    tagClosingTests.runTagClosingTests();
    alert('标签闭合测试已运行，请查看控制台输出');
  };

  // 自动修复XML
  const autoFixXML = async () => {
    const content = xmlContent();
    if (!content.trim()) {
      alert('请输入XML内容');
      return;
    }

    setIsFixing(true);

    try {
      setTimeout(() => {
        const fixer = new JATSAutoFixer();
        const result = fixer.autoFix(content);

        setFixResult(result);

        if (result.fixed) {
          setXmlContent(result.fixedXML);
          // 自动校验修复后的XML
          const validationResult = jatsValidator.validate(result.fixedXML);
          setValidationResult(validationResult);
        }

        setIsFixing(false);
      }, 100);
    } catch (error) {
      console.error('自动修复过程中发生错误:', error);
      setIsFixing(false);
      alert('自动修复过程中发生错误');
    }
  };

  // 计算统计信息
  const stats = createMemo(() => {
    const result = validationResult();
    if (!result) return null;
    
    return {
      isValid: result.isValid,
      errorCount: result.errors.length,
      warningCount: result.warnings.length,
      processingTime: Math.round(result.summary.processingTime * 100) / 100
    };
  });

  // 获取错误类型的样式类
  const getErrorTypeClass = (type: string) => {
    switch (type) {
      case 'error': return 'text-error';
      case 'warning': return 'text-warning';
      case 'info': return 'text-info';
      default: return 'text-base-content';
    }
  };

  // 获取错误类型的图标
  const getErrorTypeIcon = (type: string) => {
    switch (type) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '•';
    }
  };

  return (
    <div class="container mx-auto p-6 max-w-7xl">
      <div class="mb-6">
        <h1 class="text-3xl font-bold mb-2">JATS XML 校验器</h1>
        <p class="text-base-content/70">
          在线校验JATS XML文档的格式正确性、标签使用和结构合规性
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：输入区域 */}
        <div class="space-y-4">
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h2 class="card-title mb-4">XML 输入</h2>
              
              {/* 选项卡 */}
              <div class="tabs tabs-boxed mb-4">
                <button 
                  class={`tab ${activeTab() === 'input' ? 'tab-active' : ''}`}
                  onClick={() => setActiveTab('input')}
                >
                  文本输入
                </button>
                <button 
                  class={`tab ${activeTab() === 'file' ? 'tab-active' : ''}`}
                  onClick={() => setActiveTab('file')}
                >
                  文件上传
                </button>
              </div>

              {/* 文本输入 */}
              <Show when={activeTab() === 'input'}>
                <div class="space-y-4">
                  <textarea
                    class="textarea textarea-bordered w-full h-64 font-mono text-sm"
                    placeholder="请输入JATS XML内容..."
                    value={xmlContent()}
                    onInput={(e) => setXmlContent(e.target.value)}
                  />
                  <div class="flex gap-2 flex-wrap">
                    <div class="dropdown">
                      <div tabindex="0" role="button" class="btn btn-secondary btn-sm">
                        加载示例 ▼
                      </div>
                      <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-64">
                        <li><a onClick={loadExample}>基础示例</a></li>
                        <li><a onClick={loadDemoExample}>Demo.xml示例</a></li>
                        <li><a onClick={loadStandardTemplate}>标准JATS模板</a></li>
                        <li><a onClick={loadTagClosingExample}>标签闭合测试</a></li>
                        <li><a onClick={loadProblematicExample}>有问题的XML</a></li>
                      </ul>
                    </div>
                    <button
                      class="btn btn-info btn-sm"
                      onClick={formatXML}
                      disabled={!xmlContent().trim()}
                    >
                      格式化XML
                    </button>
                    <button
                      class="btn btn-warning btn-sm"
                      onClick={autoFixXML}
                      disabled={!xmlContent().trim() || isFixing()}
                    >
                      {isFixing() ? '修复中...' : '自动修复'}
                    </button>
                    <button
                      class="btn btn-accent btn-sm"
                      onClick={testDemoCompliance}
                      disabled={!xmlContent().trim()}
                    >
                      检查标准合规性
                    </button>
                    <button
                      class="btn btn-info btn-sm"
                      onClick={runTagClosingTests}
                    >
                      运行标签测试
                    </button>
                    <button
                      class="btn btn-ghost btn-sm"
                      onClick={clearContent}
                    >
                      清空
                    </button>
                  </div>
                </div>
              </Show>

              {/* 文件上传 */}
              <Show when={activeTab() === 'file'}>
                <div class="space-y-4">
                  <input
                    type="file"
                    accept=".xml,.txt"
                    class="file-input file-input-bordered w-full"
                    onChange={handleFileUpload}
                  />
                  <Show when={selectedFile()}>
                    <div class="alert alert-info">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>已选择文件: {selectedFile()?.name}</span>
                    </div>
                  </Show>
                </div>
              </Show>

              {/* 校验选项 */}
              <div class="divider">校验选项</div>
              <div class="space-y-4">
                <div class="form-control">
                  <label class="label cursor-pointer">
                    <span class="label-text">自动格式化</span>
                    <input
                      type="checkbox"
                      class="checkbox checkbox-secondary"
                      checked={autoFormat()}
                      onChange={(e) => setAutoFormat(e.target.checked)}
                    />
                  </label>
                  <div class="label">
                    <span class="label-text-alt text-base-content/60">
                      校验前自动格式化XML内容，包括处理缩进和美化格式
                    </span>
                  </div>
                </div>

                <div class="form-control">
                  <label class="label cursor-pointer">
                    <span class="label-text">严格模式</span>
                    <input
                      type="checkbox"
                      class="checkbox checkbox-primary"
                      checked={strictMode()}
                      onChange={(e) => setStrictMode(e.target.checked)}
                    />
                  </label>
                  <div class="label">
                    <span class="label-text-alt text-base-content/60">
                      严格模式会检查更多的JATS标准合规性问题
                    </span>
                  </div>
                </div>
              </div>

              {/* 校验按钮 */}
              <div class="card-actions justify-end mt-4">
                <button 
                  class={`btn btn-primary ${isValidating() ? 'loading' : ''}`}
                  onClick={validateXML}
                  disabled={isValidating() || !xmlContent().trim()}
                >
                  {isValidating() ? '校验中...' : '开始校验'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：结果区域 */}
        <div class="space-y-4">
          {/* 修复结果 */}
          <Show when={fixResult()}>
            <div class="card bg-base-100 shadow-lg">
              <div class="card-body">
                <h2 class="card-title mb-4">🔧 自动修复结果</h2>

                <Show when={fixResult()!.fixed}>
                  <div class="alert alert-success mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>成功应用了 {fixResult()!.appliedFixes.length} 个修复</span>
                  </div>

                  <div class="mb-4">
                    <h3 class="font-semibold text-success mb-2">✨ 应用的修复 ({fixResult()!.appliedFixes.length})</h3>
                    <div class="space-y-1">
                      <For each={fixResult()!.appliedFixes}>
                        {(fix: string) => (
                          <div class="badge badge-success badge-outline">{fix}</div>
                        )}
                      </For>
                    </div>
                  </div>
                </Show>

                <Show when={!fixResult()!.fixed}>
                  <div class="alert alert-info">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>未发现需要修复的问题</span>
                  </div>
                </Show>

                <Show when={fixResult()!.remainingErrors.length > 0}>
                  <div class="mb-4">
                    <h3 class="font-semibold text-warning mb-2">⚠️ 剩余问题 ({fixResult()!.remainingErrors.length})</h3>
                    <div class="space-y-2 max-h-32 overflow-y-auto">
                      <For each={fixResult()!.remainingErrors}>
                        {(error: ValidationError) => (
                          <div class="alert alert-warning py-2">
                            <div class="text-sm">
                              <div class="font-medium">{error.message}</div>
                              <div class="opacity-70">代码: {error.code}</div>
                            </div>
                          </div>
                        )}
                      </For>
                    </div>
                  </div>
                </Show>
              </div>
            </div>
          </Show>

          <Show when={validationResult()}>
            <div class="card bg-base-100 shadow-lg">
              <div class="card-body">
                <h2 class="card-title mb-4">📋 校验结果</h2>
                
                {/* 统计信息 */}
                <Show when={stats()}>
                  <div class="stats stats-vertical lg:stats-horizontal shadow mb-4">
                    <div class="stat">
                      <div class="stat-figure">
                        <div class={`text-2xl ${stats()!.isValid ? 'text-success' : 'text-error'}`}>
                          {stats()!.isValid ? '✅' : '❌'}
                        </div>
                      </div>
                      <div class="stat-title">状态</div>
                      <div class={`stat-value text-lg ${stats()!.isValid ? 'text-success' : 'text-error'}`}>
                        {stats()!.isValid ? '有效' : '无效'}
                      </div>
                    </div>
                    
                    <div class="stat">
                      <div class="stat-figure text-error">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </div>
                      <div class="stat-title">错误</div>
                      <div class="stat-value text-error">{stats()!.errorCount}</div>
                    </div>
                    
                    <div class="stat">
                      <div class="stat-figure text-warning">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </div>
                      <div class="stat-title">警告</div>
                      <div class="stat-value text-warning">{stats()!.warningCount}</div>
                    </div>
                  </div>
                </Show>

                {/* 处理时间 */}
                <Show when={stats()}>
                  <div class="text-sm text-base-content/60 mb-4">
                    处理时间: {stats()!.processingTime}ms
                  </div>
                </Show>

                {/* 错误列表 */}
                <Show when={validationResult()!.errors.length > 0}>
                  <div class="mb-4">
                    <h3 class="font-semibold text-error mb-2">错误 ({validationResult()!.errors.length})</h3>
                    <div class="space-y-2 max-h-64 overflow-y-auto">
                      <For each={validationResult()!.errors}>
                        {(error: ValidationError) => (
                          <div class="alert alert-error py-2">
                            <div class="flex-1">
                              <div class="flex items-start gap-2">
                                <span class="text-lg">{getErrorTypeIcon(error.type)}</span>
                                <div class="flex-1">
                                  <div class="font-medium">{error.message}</div>
                                  <div class="text-sm opacity-70">
                                    代码: {error.code}
                                    {error.line && ` | 第${error.line}行`}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </For>
                    </div>
                  </div>
                </Show>

                {/* 警告列表 */}
                <Show when={validationResult()!.warnings.length > 0}>
                  <div class="mb-4">
                    <h3 class="font-semibold text-warning mb-2">警告 ({validationResult()!.warnings.length})</h3>
                    <div class="space-y-2 max-h-64 overflow-y-auto">
                      <For each={validationResult()!.warnings}>
                        {(warning: ValidationError) => (
                          <div class="alert alert-warning py-2">
                            <div class="flex-1">
                              <div class="flex items-start gap-2">
                                <span class="text-lg">{getErrorTypeIcon(warning.type)}</span>
                                <div class="flex-1">
                                  <div class="font-medium">{warning.message}</div>
                                  <div class="text-sm opacity-70">
                                    代码: {warning.code}
                                    {warning.line && ` | 第${warning.line}行`}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </For>
                    </div>
                  </div>
                </Show>

                {/* 成功消息 */}
                <Show when={validationResult()!.isValid}>
                  <div class="alert alert-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>恭喜！您的JATS XML文档通过了所有校验检查。</span>
                  </div>
                </Show>
              </div>
            </div>
          </Show>

          {/* 帮助信息 */}
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title text-lg">使用说明</h3>
              <div class="text-sm space-y-2">
                <p>• <strong>自动修复</strong>: 智能修复常见的XML和JATS问题，如未转义字符、缺失属性等</p>
                <p>• <strong>自动格式化</strong>: 校验前自动格式化XML，包括处理缩进和美化格式</p>
                <p>• <strong>格式化XML</strong>: 手动格式化当前XML内容，优化可读性</p>
                <p>• <strong>示例加载</strong>: 提供多种示例XML，包括标准示例、demo.xml和有问题的XML</p>
                <p>• <strong>格式检查</strong>: 检查XML语法正确性，包括标签闭合、字符转义等</p>
                <p>• <strong>JATS合规性</strong>: 检查是否符合JATS标准，包括DTD声明、根元素等</p>
                <p>• <strong>结构验证</strong>: 检查元素层次结构是否符合JATS规范</p>
                <p>• <strong>属性验证</strong>: 检查元素属性是否正确使用</p>
                <p>• <strong>严格模式</strong>: 启用更严格的JATS标准检查</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
