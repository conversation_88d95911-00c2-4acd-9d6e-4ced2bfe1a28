import {DocSumItem, parseDoc, IArticleId, IIdResponse, IPmidResult, IPmidResponse, IContentId} from "./model";

// PubStatus ::= INTEGER {            -- points of publication
//     received  (1) ,            -- date manuscript received for review
//         accepted  (2) ,            -- accepted for publication
//         epublish  (3) ,            -- published electronically by publisher
//     ppublish  (4) ,            -- published in print by publisher
//     revised   (5) ,            -- article revised by publisher/author
//     pmc       (6) ,            -- article first appeared in PubMed Central
//     pmcr      (7) ,            -- article revision in PubMed Central
//     pubmed    (8) ,            -- article citation first appeared in PubMed
//     pubmedr   (9) ,            -- article citation revision in PubMed
//     aheadofprint (10),         -- epublish, but will be followed by print
//     premedline (11),           -- date into PreMedline status
//     medline    (12),           -- date made a MEDLINE record
//     other    (255) }
// ppublish+epublish : 256
// 状态为空： 258， id: 32226532


export default class commonImpl implements parseDoc {

    fileName: string
    fileKey: number
    contents: string []
    data: IPmidResponse
    contentId: IContentId[]

    constructor(fileName, fileKey, contents, data, contentId) {
        this.fileName = fileName
        this.fileKey = fileKey
        this.contents = contents
        this.data = data
        this.contentId = contentId
    }

    showDocIndex(content: string, index: number, id: number): string {
        return `[${index}] <em><strong>${content}</strong></em>`
    }

    getDocSumList(contents: string[], result: IPmidResult): string[] {
        try {
            return contents.map((child, index) => {
                let tempContentId = this.contentId.find(item => item.content === child)
                if (!tempContentId || tempContentId.id < 1) {// 文件内容中的id
                    return this.showDocIndex(child, index + 1, tempContentId.id)
                } else {
                    let item: IIdResponse = result[tempContentId.id]
                    let c = child
                    c = c.replace(/[\r\n\t\s]/g, "")
                        .replace(/(\(|\))/g, '')
                        .replace('–', '-')
                        .toLowerCase()
                    let t = item.title
                    t = t.replace(/[\r\n\t\s]/g, "")
                        .replace(/<[^<>]+?>/g, '')
                        .replace(/(\(|\))/g, '')
                        .replace('–', '-')
                        .replace(/\.$/, '')
                        .toLowerCase()
                    if (c.indexOf(t) < 0) {
                        return this.showDocIndex(child, index + 1, tempContentId.id)
                    }
                    let title = item.title.replace(/\[|\]|<(.*?)>|<\/(.*?)>|<(.*?)\/>/g, "")
                    let doi = this.getArticleIdProperty(item.articleids, "doi")
                    let sumItem = {
                        id: item.uid,
                        title: title,
                        source: item.source,
                        pubDate: item.pubdate,
                        volume: item.volume,
                        issue: item.issue,
                        pages: this.dealPages(item.pages),
                        doi: doi,
                        pubStatus: item.pubstatus,
                        authors: this.getAuthors(item),
                        eLocationId: item.elocationid
                    }
                    return this.convertDocSumToStr(index + 1, sumItem)
                }
            })
        } catch (e) {
            console.log(e)
            return []
        }
    }

    dealPages(pageStr: string): string {
        if (!pageStr) {
            return ''
        }
        let pageArr = pageStr.replace(/;.*/,'').split("-")
        if (pageArr.length === 2) {
            if (pageArr[0].length != pageArr[1].length) {
                let sub = pageArr[0].length - pageArr[1].length
                if (sub > 0) {
                    pageArr[1] = `${pageArr[0].slice(0, sub)}${pageArr[1]}`
                }
            }
            return pageArr.join("-")
        }
        return pageStr
    }

    convertDocSumToStr(refId: number, docSumItem: DocSumItem): string {
        let result = "["+refId+"]" + " "
        result += docSumItem.authors ? docSumItem.authors + ". " : ""
        if (docSumItem.pubStatus === '10') {
            result += docSumItem.title ? docSumItem.title?.substring(0, docSumItem.title.lastIndexOf(".")) + "[J/OL]. " : ""
        } else {
            result += docSumItem.title ? docSumItem.title?.substring(0, docSumItem.title.lastIndexOf(".")) + "[J]. " : ""
        }
        result += docSumItem.source ? docSumItem.source + "，" : ""
        let pubDate = docSumItem.pubDate.split(' ')
        if (docSumItem.pubStatus === "10") {//'aheadofprint'
            result += pubDate.length ? pubDate[0] + "，" : ""
            result += '[Epub ahead of print].'
            result += docSumItem.doi ? ' doi: ' + docSumItem.doi + ". " : ""
        } else if (docSumItem.pages) {
            result += pubDate.length ? pubDate[0] + "，" : ""
            result += docSumItem.volume ? docSumItem.volume + "" : ""
            result += docSumItem.issue && docSumItem.volume ? "(" + docSumItem.issue + ")" + "：" : "："
            result += docSumItem.pages + "."
        } else if (docSumItem.pubStatus != '258' && docSumItem.eLocationId) {
            let matchReg = /(?<=pii\:).*?(?=\.)/
            let piiArr = docSumItem.eLocationId.match(matchReg)
            if (!piiArr || !piiArr.length) {
                result += pubDate.length ? pubDate[0] + "." : ""
                result += docSumItem.doi ? ' doi: ' + docSumItem.doi + "." : ""
            } else if (docSumItem.pubStatus === "3") {//'epublish'
                let pii = piiArr.length ? piiArr[0].trim() : ""
                pii = 'E' === pii.charAt(0)?.toUpperCase() ? pii.slice(1) : pii
                result += pubDate.length ? pubDate[0] + "，" : ""
                result += docSumItem.volume ? docSumItem.volume + "" : ""
                result += docSumItem.issue && docSumItem.volume ? "(" + docSumItem.issue + ")" + "：" : "："
                result += pii ? pii + "." : ""
            } else if (docSumItem.pubStatus === "4"//'ppublish'
                || docSumItem.pubStatus === '256' //'ppublish +epublish'
                || docSumItem.pubStatus === '257') {//'epublish + ppublish'
                let pii = piiArr.length ? piiArr[0].trim() : ""
                result += pubDate.length ? pubDate[0] + ", " : ""
                result += docSumItem.volume ? docSumItem.volume + "" : ""
                result += docSumItem.issue && docSumItem.volume ? "(" + docSumItem.issue + ")" + "：" : "："
                result += pii ? pii + "." : ""
            }
        } else if (docSumItem.eLocationId) {
            let matchReg = /(?<=pii\:).*?(?=\.)/
            let piiArr = docSumItem.eLocationId.match(matchReg)
            if (!piiArr || !piiArr.length) {
                result += pubDate.length ? pubDate[0] + "." : ""
                result += docSumItem.doi ? ' doi: ' + docSumItem.doi + "." : ""
            } else {
                let pii = piiArr.length ? piiArr[0].trim() : ""
                result += pubDate.length ? pubDate[0] + "，" : ""
                result += docSumItem.volume ? docSumItem.volume + "" : ""
                result += docSumItem.issue && docSumItem.volume ? "(" + docSumItem.issue + ")" + "：" : "："
                result += pii ? "e"+ pii + "." : ""
            }
        } else if (pubDate.length) {
            result += pubDate[0] + "，"
        }
        return result
    }


    getAuthors(item: IIdResponse): string {
        let lastItem = ''
        let authorStr = ''
        let authorCount = 0
        let collectiveCount = 0
        let count = 0
        let len = 3
        Array.prototype.map.call(item.authors, child => {
            if (count < len) {
                if (lastItem === 'CollectiveName') {
                    collectiveCount++
                    authorStr += "; "
                } else if (lastItem === 'Author') {
                    authorCount++
                }
                if (child.authtype === 'CollectiveName') {
                    authorStr += lastItem === "Author" ? "; " : ""
                    authorStr += child.name.substring(0, child.name.lastIndexOf('.'))
                } else {
                    authorStr += lastItem === "Author" ? ", " : ""
                    let tempName = child.name
                    let tArr = tempName.split(" ")
                    if (tArr.length === 2 && tArr[1].match(/[A-Z]{2}/)) {
                        tempName = tArr[0]
                        tempName = `${tArr[0]} ${tArr[1].split("").join(" ")}`
                    }
                    authorStr += tempName
                }
                lastItem = child.authtype
            }
            count++
        })
        if (count > len) {
            authorStr += '，et al'
        }
        return authorStr
    }


    getArticleIdProperty(data: IArticleId[], type: string): string {
        if (!data?.length) {
            return ''
        }
        let item = data.find(item => item.idtype === type)
        return item ? item[type] : ''
    }

    convertSummary(contents: string[], result: IPmidResult): string[] {
        let docSumList = this.getDocSumList(contents, result)
        return docSumList
    }

    getSummaryError(): string {
        let error = ''
        return error || '文件无具体转换内容,请换个文件操作'
    }


    appendToView(docStr: string[]): string {
        let elem = document.getElementById('view1')
        let childElem = document.createElement('div')
        childElem.id = `File_${this.fileKey}`
        childElem.className = 'file_segment'
        let ulElem = document.createElement('ul')
        docStr.forEach(item => {
            let subElem = document.createElement('ol')
            subElem.style.marginBottom = "10px"
            subElem.innerHTML = item
            ulElem.appendChild(subElem)
        })
        childElem.appendChild(ulElem)
        elem.appendChild(childElem)
        return childElem.innerHTML
    }


    render(): Promise<string> {
        let res = this.convertSummary(this.contents, this.data.result)
        if (res && res.length) {
            return Promise.resolve(this.appendToView(res))
        } else {
            let error = this.getSummaryError()
            return Promise.reject(error)
        }
    }

}
