import { promisify } from 'util';
import { AuthorDetailFoPubmed, JCRInfo } from './types';
import mammoth from 'mammoth';

// 用于存储JCR数据的IndexedDB实例
let db: IDBDatabase | null = null;

// 将IndexedDB操作转换为Promise
const indexedDBRequest = (request: IDBRequest): Promise<any> => {
  return new Promise((resolve, reject) => {
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
};

// 初始化JCR数据库
export const initJCRDatabase = async () => {
  if (!window.indexedDB) {
    console.error('Your browser does not support IndexedDB');
    return;
  }

  return new Promise((resolve, reject) => {
    const request = window.indexedDB.open('JCRDatabase', 1);

    request.onerror = () => {
      console.error('Error opening database');
      reject(request.error);
    };

    request.onsuccess = () => {
      db = request.result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains('journals')) {
        db.createObjectStore('journals', { keyPath: 'issn' });
      }
    };
  });
};

// 获取期刊的JCR分区
export const getJournalQuartile = async (issn: string): Promise<string | null> => {
  if (!db) {
    await initJCRDatabase();
  }

  try {
    const transaction = db!.transaction(['journals'], 'readonly');
    const store = transaction.objectStore('journals');
    const result = await indexedDBRequest(store.get(issn));
    return result?.quartile || null;
  } catch (error) {
    console.error('Error getting journal quartile:', error);
    return null;
  }
};

export const getJCRInfo = async (issn: string): Promise<JCRInfo | null> => {
  if (!db) {
    await initJCRDatabase();
  }

  try {
    const transaction = db!.transaction(['journals'], 'readonly');
    const store = transaction.objectStore('journals');
    const result = await indexedDBRequest(store.get(issn));
    
    if (!result) {
      return null;
    }

    return {
      issn: result.issn,
      title: result.title,
      quartile: result.quartile,
      impactFactor: result.impactFactor,
      category: result.category
    };
  } catch (error) {
    console.error('Error getting JCR info:', error);
    return null;
  }
};

export const closeJCRDatabase = () => {
  if (db) {
    db.close();
    db = null;
  }
};

export const extractKeywordsFromDocx = async (file: File): Promise<string> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });
    const text = result.value;
    
    // 查找 Keywords 或 Key words 后面的内容
    const keywordsRegex = /(?:Keywords|Key words)\s*[:：]?\s*(.*?)(?:\n|$)/i;
    const match = text.match(keywordsRegex);
    
    return match ? match[1].trim() : '';
  } catch (error) {
    console.error('Error parsing docx:', error);
    throw new Error('无法解析文档');
  }
};

export const filterAuthorsByJCR = (authors: AuthorDetailFoPubmed[], quarters: string[]): AuthorDetailFoPubmed[] => {
  return authors.filter(author => {
    const quartile = author.publications[0]?.IFQuartile;
    return quartile && quarters.includes(quartile);
  });
};
