import {DocSumItem, RefIdItem, RefList} from "../zhRefList/model";
import {Api} from "../../restful/api/restApi";


export default class CJTSRefImpl implements RefList {

    file: string
    fileKey: string
    refIdList: RefIdItem[]
    xmlString: string

    constructor(file, fileKey, refIdList, xmlString) {
        this.file = file
        this.fileKey = fileKey
        this.refIdList = refIdList
        this.xmlString = xmlString
    }

    parseXML(xmlStr: string): Document {
        let parser = new DOMParser()
        let xmlDoc = parser.parseFromString(xmlStr, 'text/xml')
        return xmlDoc
    }


    getSummary(ids): Promise<string> {
        return Api.get("/entrez/eutils/esummary.fcgi", {db: "pubmed", id: ids}, {}).then(res => {
            return res
        })
    }


    getDocSumList(refIdList: RefIdItem[], xmlDoc: Document): string[] {
        let list = xmlDoc.querySelectorAll('DocSum')
        if (!list?.length) {
            return []
        }
        return refIdList.map(child => {
            if (!child.pmid) {
                return child.rId + ". "
            } else {
                let item = Array.prototype.find.call(list, item => child.pmid === item.querySelector('Id')?.textContent)
                let id = item.querySelector('Id')?.textContent
                let source = item.querySelector('Item[Name=\"Source\"]')?.textContent
                let pubDate = item.querySelector('Item[Name=\"PubDate\"]')?.textContent
                let volume = item.querySelector('Item[Name=\"Volume\"]')?.textContent
                let authorDoc = item.querySelector("Item[Name=\"AuthorList\"]")
                let authors = this.getAuthors(authorDoc)
                let title = item.querySelector('Item[Name=\"Title\"]')?.textContent?.replace(/\[|\]/g, "")
                let issue = item.querySelector('Item[Name=\"Issue\"]')?.textContent
                let pages = this.dealPages(item)
                let doi = item.querySelector('Item[Name=\"ArticleIds\"] Item[Name=\"doi\"]')?.textContent
                let pubStatus = item.querySelector('Item[Name=\"PubStatus\"]')?.textContent
                let eLocationId = item.querySelector('Item[Name=\"ELocationID\"]')?.textContent
                let sumItem = {
                    id,
                    title,
                    source,
                    pubDate,
                    volume,
                    issue,
                    pages,
                    doi,
                    pubStatus,
                    authors,
                    eLocationId
                }
                return this.convertDocSumToStr(child, sumItem)
            }
        })

    }


    convertDocSumToStr(refIdItem: RefIdItem, docSumItem: DocSumItem): string {
        let result = refIdItem?.rId + "．"
        result += docSumItem.authors ? docSumItem.authors + "．" : ""
        if(docSumItem.pubStatus === 'aheadofprint'){
            result += docSumItem.title?.substring(0, docSumItem.title.lastIndexOf(".")) + "[J/OL]．"
        }else{
            result += docSumItem.title?.substring(0, docSumItem.title.lastIndexOf(".")) + "[J]．"
        }

        result += docSumItem.source + "，"
        let pubDate = docSumItem.pubDate.split(' ')

        if (docSumItem.pubStatus === 'aheadofprint') {
            result += pubDate.length ? pubDate[0] + "，" : ""
            result += 'Epub ahead of print．'
        } else if (docSumItem.pages) {
            result += pubDate.length ? pubDate[0] + "，" : ""
            result += docSumItem.volume ? docSumItem.volume + "" : ""
            result += docSumItem.issue ? "（" + docSumItem.issue + "）" + "：" : "："
            result += docSumItem.pages + "．"
        } else if (docSumItem.pubStatus && docSumItem.eLocationId) {
            let matchReg = /(?<=pii\:).*?(?=\.)/
            let piiArr = docSumItem.eLocationId.match(matchReg)
            if (!piiArr || !piiArr.length) {
                result += pubDate.length ? pubDate[0] + "．" : ""
                result += docSumItem.doi ? ' doi：' + docSumItem.doi + "．" : ""

            } else if (docSumItem.pubStatus === 'epublish') {
                let pii = piiArr.length ? piiArr[0].trim() : ""
                pii = 'E' === pii.charAt(0)?.toUpperCase() ? pii.slice(1) : pii
                result += pubDate.length ? pubDate[0] + "，" : ""
                result += docSumItem.volume ? docSumItem.volume + "" : ""
                result += docSumItem.issue ? "（" + docSumItem.issue + "）" + "：" : "："
                result += pii ?  pii + "．" : ""
            } else if (docSumItem.pubStatus === 'ppublish'
                || docSumItem.pubStatus === 'ppublish+epublish'
                || docSumItem.pubStatus === 'epublish+ppublish') {
                let pii = piiArr.length ? piiArr[0].trim() : ""
                result += pubDate.length ? pubDate[0] + "，" : ""
                result += docSumItem.volume ? docSumItem.volume + "" : ""
                result += docSumItem.issue ? "（" + docSumItem.issue + "）" + "：" : "："
                result += pii ?  pii + "．" : ""
            }
        } else if (pubDate.length) {
            result += pubDate[0] + "，"
        }
        return result
    }

    dealPages(xmlDoc: Document): string {
        let pageStr = xmlDoc.querySelector('Item[Name=\"Pages\"]')?.textContent
        if (!pageStr) {
            return ''
        }
        let pageArr = pageStr.split("-")
        if (pageArr.length === 2) {
            if (pageArr[0].length != pageArr[1].length) {
                let sub = pageArr[0].length - pageArr[1].length
                if (sub > 0) {
                    pageArr[1] = `${pageArr[0].slice(0, sub)}${pageArr[1]}`
                }
            }
            return pageArr.join("-")
        }
        return pageStr
    }

    getAuthors(xmlDoc: Document): string {
        let list = xmlDoc.querySelectorAll('Item')
        let lastItem = ''
        let authorStr = ''
        let authorCount = 0
        let collectiveCount = 0
        let count = 0
        let len = 3
        Array.prototype.map.call(list, item => {
            if (count < len) {
                if (lastItem === 'CollectiveName') {
                    collectiveCount++
                    authorStr += "；"
                } else if (lastItem === 'Author') {
                    authorCount++
                }
                if (item.getAttribute('Name') === 'CollectiveName') {
                    authorStr += lastItem === "Author" ? "；" : ""
                    authorStr += item.textContent?.substring(0, item.textContent.lastIndexOf('.'))
                } else {
                    authorStr += lastItem === "Author" ? "，" : ""
                    authorStr += item.textContent
                }
                lastItem = item.getAttribute('Name')
            }
            count++
        })
        if (count > len) {
            authorStr += '，et al'
        }
        return authorStr
    }


    convertSummary(refIdList: RefIdItem[], xmlString: string): string[] {
        let xmlDoc = this.parseXML(xmlString)
        let docSumList = this.getDocSumList(refIdList, xmlDoc)
        return docSumList
    }

    getSummaryError(xmlString: string): string {
        let xmlDoc = this.parseXML(xmlString)
        let error = xmlDoc.querySelector('ERROR')?.textContent
        return error || '文件无具体转换内容,请换个文件操作'
    }


    appendToView(docStr: string[]): string {
        let elem = document.getElementById('view1')
        let childElem = document.createElement('div')
        childElem.id = `File_${this.fileKey}`
        childElem.className = 'file_segment'
        let ulElem = document.createElement('ul')
        docStr.forEach(item => {
            let subElem = document.createElement('ol')
            subElem.style.marginBottom = "10px"
            subElem.innerHTML = item
            ulElem.appendChild(subElem)
        })
        childElem.appendChild(ulElem)
        elem.appendChild(childElem)
        return childElem.innerHTML
    }


    render(): Promise<string> {
        let result = this.convertSummary(this.refIdList, this.xmlString)
        if (result && result.length) {
            return Promise.resolve(this.appendToView(result))
        } else {
            let error = this.getSummaryError(this.xmlString)
            return Promise.reject(error)
        }
    }

}
