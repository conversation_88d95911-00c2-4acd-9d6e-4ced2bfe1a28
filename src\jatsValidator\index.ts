// JATS XML校验器主入口文件

import { JATSValidator, validateJATSXML, createValidator } from './core';
import { JATS_RULES, JATS_DTD_DECLARATIONS } from './rules';
import { JATSAutoFixer, autoFixJATSXML } from './autoFixer';
import type {
  ValidationError,
  ValidationResult,
  JATSElement,
  JATSAttribute,
  JATSValidationRules,
  ValidatorOptions
} from './types';
import type { FixResult } from './autoFixer';

// 重新导出所有内容
export { JATSValidator, validateJATSXML, createValidator };
export { JATS_RULES, JATS_DTD_DECLARATIONS };
export { JATSAutoFixer, autoFixJATSXML };
export type {
  ValidationError,
  ValidationResult,
  JATSElement,
  JATSAttribute,
  JATSValidationRules,
  ValidatorOptions,
  FixResult
};

// 便捷的校验函数，供其他模块使用
export const jatsValidator = {
  /**
   * 快速校验JATS XML
   * @param xmlContent XML内容
   * @param options 校验选项
   * @returns 校验结果
   */
  validate: (xmlContent: string, options?: ValidatorOptions) => {
    return validateJATSXML(xmlContent, options);
  },

  /**
   * 创建校验器实例
   * @param options 校验选项
   * @returns 校验器实例
   */
  createValidator: (options?: ValidatorOptions) => {
    return createValidator(options);
  },

  /**
   * 检查XML是否为有效的JATS格式
   * @param xmlContent XML内容
   * @returns 是否有效
   */
  isValidJATS: (xmlContent: string): boolean => {
    const result = validateJATSXML(xmlContent, { strictMode: false });
    return result.isValid;
  },

  /**
   * 获取XML的错误摘要
   * @param xmlContent XML内容
   * @returns 错误摘要
   */
  getErrorSummary: (xmlContent: string) => {
    const result = validateJATSXML(xmlContent);
    return {
      hasErrors: result.errors.length > 0,
      errorCount: result.errors.length,
      warningCount: result.warnings.length,
      firstError: result.errors[0]?.message || null,
      processingTime: result.summary.processingTime
    };
  },

  /**
   * 获取详细的校验报告
   * @param xmlContent XML内容
   * @param options 校验选项
   * @returns 详细报告
   */
  getDetailedReport: (xmlContent: string, options?: ValidatorOptions) => {
    const result = validateJATSXML(xmlContent, options);

    return {
      ...result,
      report: {
        title: 'JATS XML校验报告',
        timestamp: new Date().toISOString(),
        summary: `发现 ${result.errors.length} 个错误，${result.warnings.length} 个警告`,
        details: {
          errors: result.errors.map(err => ({
            severity: 'error',
            code: err.code,
            message: err.message,
            location: err.line ? `第${err.line}行` : '未知位置'
          })),
          warnings: result.warnings.map(warn => ({
            severity: 'warning',
            code: warn.code,
            message: warn.message,
            location: warn.line ? `第${warn.line}行` : '未知位置'
          }))
        }
      }
    };
  },

  /**
   * 格式化XML内容
   * @param xmlContent XML内容
   * @returns 格式化后的XML
   */
  formatXML: (xmlContent: string) => {
    const validator = createValidator();
    return validator.formatXML(xmlContent);
  },

  /**
   * 自动修复XML问题
   * @param xmlContent XML内容
   * @returns 修复结果
   */
  autoFix: (xmlContent: string) => {
    return autoFixJATSXML(xmlContent);
  }
};

// 默认导出
export default jatsValidator;
