import { createSignal, createResource, For, Show } from 'solid-js';
import { PubMedService } from '../services/PubMedService';
import { KeywordMatchingService } from '../services/KeywordMatchingService';
import { ORCIDService } from '../services/ORCIDService';
import * as mammoth from 'mammoth';

const PubMedSearch = () => {
  const [keywords, setKeywords] = createSignal('');
  const [maxResults, setMaxResults] = createSignal(10); // 调试开发，默认10篇
  const [sortBy, setSortBy] = createSignal('relevance');
  const [keywordMode, setKeywordMode] = createSignal('SMART'); // 'OR', 'AND', 或 'SMART'
  const [isSearching, setIsSearching] = createSignal(false);
  const [searchResults, setSearchResults] = createSignal([]);
  const [error, setError] = createSignal('');
  const [uploadedFile, setUploadedFile] = createSignal(null);
  const [extractedContent, setExtractedContent] = createSignal(null);
  const [selectedAuthors, setSelectedAuthors] = createSignal(new Set());
  const [excludeCountries, setExcludeCountries] = createSignal(['China', 'Russia', 'Belarus', 'North Korea', 'Cuba']);
  const [includeCountries, setIncludeCountries] = createSignal([]);
  const [filterMode, setFilterMode] = createSignal('exclude'); // 'none', 'exclude', 'include'
  const [searchProgress, setSearchProgress] = createSignal({
    total: 0,
    completed: 0,
    stage: '',
    isVisible: false
  });

  // 处理文件上传
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.docx')) {
      setError('请上传 .docx 格式的文件');
      return;
    }

    setUploadedFile(file);
    setError('');

    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });
      
      // 提取标题（假设第一行是标题）
      const lines = result.value.split('\n').filter(line => line.trim());
      const title = lines[0] || '';
      
      // 从文档内容中提取关键词
      let extractedKeywords = [];

      // 首先尝试查找 Keywords: 标识后的内容
      const keywordsMatch = result.value.match(/Keywords?:\s*([^\n\r]+)/i);
      if (keywordsMatch) {
        // 提取 Keywords: 后面的内容，按逗号、分号或换行分割
        extractedKeywords = keywordsMatch[1]
          .split(/[,;，；\n\r]/)
          .map(k => k.trim())
          .filter(k => k.length > 2);
      }

      // 如果没有找到 Keywords: 标识，则使用通用关键词提取
      if (extractedKeywords.length === 0) {
        extractedKeywords = KeywordMatchingService.extractKeywordsFromText(
          result.value,
          10
        );
      }

      setExtractedContent({
        title,
        keywords: extractedKeywords,
        content: result.value
      });

      // 自动填充关键词
      if (extractedKeywords.length > 0) {
        setKeywords(extractedKeywords.join(', '));
      }

    } catch (err) {
      console.error('文件解析错误:', err);
      setError('文件解析失败，请确保文件格式正确');
    }
  };

  // 执行搜索
  const performSearch = async () => {
    const keywordList = keywords().split(',').map(k => k.trim()).filter(k => k);
    if (keywordList.length === 0) {
      setError('请输入至少一个关键词');
      return;
    }

    setIsSearching(true);
    setError('');
    setSearchResults([]);
    setSelectedAuthors(new Set());

    // 初始化进度条
    setSearchProgress({
      total: 7, // 总共7个主要步骤（包含PubMed的4个步骤）
      completed: 0,
      stage: '开始搜索...',
      isVisible: true
    });

    try {
      // 构建查询，根据关键词模式选择不同的查询策略
      let searchQuery;
      if (keywordMode() === 'SMART') {
        searchQuery = generateSmartQuery(keywordList);
      } else {
        const queryOperator = keywordMode() === 'AND' ? ' AND ' : ' OR ';
        searchQuery = keywordList.map(keyword => `"${keyword}"`).join(queryOperator);
      }

      const searchParams = {
        query: searchQuery,
        maxResults: maxResults(), // 使用用户选择的数量
        sort: sortBy(),
        keywords: keywordList // 传递关键词用于预筛选
      };

      // 获取文章列表（带进度回调）
      const progressCallback = (current, _total, stage) => {
        setSearchProgress(prev => ({
          ...prev,
          completed: current,
          stage: stage
        }));
      };

      let articles = await PubMedService.searchAndFetchArticles(searchParams, progressCallback);

      if (articles.length === 0) {
        setError('未找到相关文章，请尝试其他关键词');
        setIsSearching(false);
        setSearchProgress(prev => ({ ...prev, isVisible: false }));
        return;
      }

      // 步骤5: 进行相关性排序
      setSearchProgress(prev => ({
        ...prev,
        completed: 5,
        stage: `正在分析 ${articles.length} 篇文章的相关性...`
      }));

      let processedArticles = KeywordMatchingService.rankArticles(articles, keywordList);

      // 调试输出：显示每篇文章的关键词和命中率
      console.log('=== 文章关键词和命中率调试信息 ===');
      processedArticles.forEach((article, index) => {
        console.log(`文章 ${index + 1}: ${article.title.substring(0, 50)}...`);
        console.log(`  PMID: ${article.pmid}`);
        console.log(`  个数命中率: ${((article.hitRate || 0) * 100).toFixed(1)}%`);
        console.log(`  综合命中率: ${((article.comprehensiveHitRate || 0) * 100).toFixed(1)}%`);
        console.log(`  命中关键词数: ${article.hitCount || 0}/${keywordList.length}`);
        console.log(`  文章关键词: ${article.keywords ? article.keywords.join(', ') : '无'}`);
        console.log(`  关键词匹配详情:`);
        if (article.keywordMatches) {
          article.keywordMatches.forEach(match => {
            if (match.score > 0) {
              console.log(`    - ${match.keyword}: ${(match.score * 100).toFixed(1)}% (${match.type})`);
            }
          });
        }
        console.log('  ---');
      });

      // 步骤6: 增强作者信息（获取 ORCID 和邮箱）
      const articlesToProcess = processedArticles.slice(0, Math.max(50, processedArticles.length));

      // 计算需要处理的 ORCID 请求总数
      const totalOrcidRequests = articlesToProcess.reduce((total, article) => {
        return total + article.authors.filter(author => author.orcid).length;
      }, 0);

      setSearchProgress(prev => ({
        ...prev,
        completed: 6,
        stage: `正在增强 ${articlesToProcess.length} 篇文章的作者信息...`,
        total: 7 + totalOrcidRequests, // 更新总数包含 ORCID 请求
      }));

      let completedOrcidRequests = 0;

      const enhancedArticles = await Promise.all(
        articlesToProcess.map(async (article) => {
          // 处理每个作者
          const enhancedAuthors = await Promise.all(
            article.authors.map(async (author) => {
              let enhancedAuthor = { ...author };

              // 只处理有 ORCID 的作者
              if (author.orcid) {
                try {
                  enhancedAuthor = await ORCIDService.enhanceAuthorInfo(author);
                  completedOrcidRequests++;

                  // 更新进度
                  setSearchProgress(prev => ({
                    ...prev,
                    completed: 6 + completedOrcidRequests,
                    stage: `正在获取作者邮箱信息... (${completedOrcidRequests}/${totalOrcidRequests})`
                  }));
                } catch (error) {
                  console.error('Error enhancing author:', error);
                  completedOrcidRequests++;
                }
              }

              // 提取国家和机构信息
              return {
                ...enhancedAuthor,
                country: enhancedAuthor.affiliation ? ORCIDService.extractCountryFromAffiliation(enhancedAuthor.affiliation) : undefined,
                institution: enhancedAuthor.affiliation ? ORCIDService.extractInstitutionFromAffiliation(enhancedAuthor.affiliation) : undefined
              };
            })
          );

          return {
            ...article,
            authors: enhancedAuthors
          };
        })
      );

      // 步骤7: 应用国家过滤
      setSearchProgress(prev => ({
        ...prev,
        completed: prev.total - 1,
        stage: '正在应用国家过滤...'
      }));

      let filteredArticles = enhancedArticles;
      if (filterMode() === 'exclude' && excludeCountries().length > 0) {
        filteredArticles = enhancedArticles.map(article => ({
          ...article,
          authors: article.authors.filter(author => {
            if (!author.country) return true; // 保留没有国家信息的作者
            return !excludeCountries().some(excludeCountry =>
              author.country.toLowerCase().includes(excludeCountry.toLowerCase())
            );
          })
        })).filter(article => article.authors.length > 0); // 移除没有作者的文章
      } else if (filterMode() === 'include' && includeCountries().length > 0) {
        filteredArticles = enhancedArticles.map(article => ({
          ...article,
          authors: article.authors.filter(author => {
            if (!author.country) return false; // 排除没有国家信息的作者
            return includeCountries().some(includeCountry =>
              author.country.toLowerCase().includes(includeCountry.toLowerCase())
            );
          })
        })).filter(article => article.authors.length > 0); // 移除没有作者的文章
      }

      // 完成搜索
      setSearchProgress(prev => ({
        ...prev,
        completed: prev.total,
        stage: `搜索完成！找到 ${filteredArticles.length} 篇相关文章`,
        isVisible: false
      }));

      // 确保至少有20篇文章
      if (filteredArticles.length < 20) {
        setError(`仅找到 ${filteredArticles.length} 篇相关文章，建议添加更多关键词或调整搜索条件`);
      }

      setSearchResults(filteredArticles);
    } catch (err) {
      console.error('搜索错误:', err);
      setError('搜索失败，请稍后重试');
      setSearchProgress(prev => ({ ...prev, isVisible: false }));
    } finally {
      setIsSearching(false);
    }
  };

  // 生成作者链接
  const getAuthorLinks = (author) => {
    return ORCIDService.generateAuthorLinks(author);
  };

  // 切换作者选择状态
  const toggleAuthorSelection = (authorKey) => {
    const newSelected = new Set(selectedAuthors());
    if (newSelected.has(authorKey)) {
      newSelected.delete(authorKey);
    } else {
      newSelected.add(authorKey);
    }
    setSelectedAuthors(newSelected);
  };

  // 全选/取消全选作者
  const toggleSelectAll = () => {
    const allAuthors = new Set();
    searchResults().forEach((article, articleIndex) => {
      article.authors.forEach((_author, authorIndex) => {
        allAuthors.add(`${articleIndex}-${authorIndex}`);
      });
    });

    if (selectedAuthors().size === allAuthors.size) {
      setSelectedAuthors(new Set());
    } else {
      setSelectedAuthors(allAuthors);
    }
  };

  // 导出选中的审稿人信息
  const exportSelectedReviewers = () => {
    const selectedReviewers = [];

    searchResults().forEach((article, articleIndex) => {
      article.authors.forEach((author, authorIndex) => {
        const authorKey = `${articleIndex}-${authorIndex}`;
        if (selectedAuthors().has(authorKey)) {
          selectedReviewers.push({
            姓名: `${author.forename} ${author.lastname}`,
            邮箱: author.email || '',
            ORCID: author.orcid || '',
            机构: author.institution || author.affiliation || '',
            国家: author.country || '',
            文章标题: article.title,
            期刊: article.journal,
            发表年份: article.pubdate.split('-')[0] || '',
            PMID: article.pmid,
            DOI: article.doi || '',
            综合命中率: article.comprehensiveHitRate ? `${(article.comprehensiveHitRate * 100).toFixed(1)}%` : '',
            个数命中率: article.hitRate ? `${(article.hitRate * 100).toFixed(0)}%` : '',
            命中关键词数: article.hitCount || '',
            ResearchGate: getAuthorLinks(author).researchgate || '',
            LinkedIn: getAuthorLinks(author).linkedin || ''
          });
        }
      });
    });

    if (selectedReviewers.length === 0) {
      setError('请先选择要导出的审稿人');
      return;
    }

    // 转换为CSV格式
    const headers = Object.keys(selectedReviewers[0]);
    const csvContent = [
      headers.join(','),
      ...selectedReviewers.map(reviewer =>
        headers.map(header => `"${reviewer[header] || ''}"`).join(',')
      )
    ].join('\n');

    // 下载CSV文件
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `审稿人信息_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 常见国家列表
  const commonCountries = [
    'United States', 'China', 'United Kingdom', 'Germany', 'Japan', 'France', 'Italy', 'Canada',
    'Australia', 'Spain', 'Netherlands', 'Switzerland', 'Sweden', 'South Korea', 'India', 'Brazil',
    'Russia', 'Belarus', 'North Korea', 'Cuba', 'Israel', 'Singapore', 'Taiwan', 'Hong Kong',
    'Denmark', 'Norway', 'Finland', 'Belgium', 'Austria', 'Poland', 'Czech Republic', 'Hungary',
    'Portugal', 'Greece', 'Turkey', 'Iran', 'Egypt', 'Saudi Arabia', 'UAE', 'Thailand', 'Malaysia',
    'Indonesia', 'Philippines', 'Vietnam', 'New Zealand'
  ];

  // 生成优化的智能组合查询
  // 生成智能权重查询策略
  const generateWeightedQuery = (keywordList, strategy = 'layered') => {
    if (keywordList.length === 0) return '';
    if (keywordList.length === 1) return `"${keywordList[0]}"`;

    console.log('=== 智能权重查询生成 ===');
    console.log(`关键词: ${keywordList.join(', ')}`);
    console.log(`策略: ${strategy}`);
    
    switch (strategy) {
      case 'field_weighted':
        return generateFieldWeightedQuery(keywordList);
      case 'layered':
        return generateLayeredQuery(keywordList);
      case 'simple':
        return generateSimpleQuery(keywordList);
      default:
        return generateLayeredQuery(keywordList);
    }
  };

  // 策略1: 字段权重查询（利用PubMed的字段权重机制）
  const generateFieldWeightedQuery = (keywordList) => {
    console.log('使用字段权重策略');
    
    const queries = [];
    
    // 最高权重：所有关键词都在标题中
    if (keywordList.length <= 4) { // 避免标题查询过长
      const titleQuery = keywordList.map(k => `"${k}"[Title]`).join(' AND ');
      queries.push(`(${titleQuery})`);
    }
    
    // 高权重：所有关键词在标题或摘要中
    const titleAbstractQuery = keywordList.map(k => `"${k}"[Title/Abstract]`).join(' AND ');
    queries.push(`(${titleAbstractQuery})`);
    
    // 中等权重：关键词在MeSH主题词中
    const meshQuery = keywordList.map(k => `"${k}"[MeSH Terms]`).join(' AND ');
    queries.push(`(${meshQuery})`);
    
    // 较低权重：关键词在任意字段
    const allFieldsQuery = keywordList.map(k => `"${k}"`).join(' AND ');
    queries.push(`(${allFieldsQuery})`);
    
    const finalQuery = queries.join(' OR ');
    console.log(`字段权重查询长度: ${finalQuery.length} 字符`);
    
    return finalQuery;
  };

  // 策略2: 分层查询（按关键词重要性分层）
  const generateLayeredQuery = (keywordList) => {
    console.log('使用分层查询策略');
    
    const queries = [];
    
    // 第一层：最重要的前3个关键词的完全匹配
    const topKeywords = keywordList.slice(0, 3);
    const topQuery = topKeywords.map(k => `"${k}"[Title/Abstract]`).join(' AND ');
    queries.push(`(${topQuery})`);
    
    // 第二层：前5个关键词的任意字段匹配
    if (keywordList.length > 3) {
      const midKeywords = keywordList.slice(0, 5);
      const midQuery = midKeywords.map(k => `"${k}"`).join(' AND ');
      queries.push(`(${midQuery})`);
    }
    
    // 第三层：递减组合（避免过多组合）
    for (let length = Math.min(keywordList.length, 4); length >= 2; length--) {
      const combination = keywordList
        .slice(0, length)
        .map(keyword => `"${keyword}"`)
        .join(' AND ');
      queries.push(`(${combination})`);
    }
    
    const finalQuery = queries.join(' OR ');
    console.log(`分层查询长度: ${finalQuery.length} 字符`);
    
    return finalQuery;
  };

  // 策略3: 简单查询（当其他策略都太长时使用）
  const generateSimpleQuery = (keywordList) => {
    console.log('使用简单查询策略');
    
    // 只使用最重要的关键词组合
    const maxKeywords = Math.min(keywordList.length, 5);
    const selectedKeywords = keywordList.slice(0, maxKeywords);
    
    const queries = [
      // 标题或摘要中的匹配
      `(${selectedKeywords.map(k => `"${k}"[Title/Abstract]`).join(' AND ')})`,
      // 任意字段匹配
      `(${selectedKeywords.map(k => `"${k}"`).join(' AND ')})`
    ];
    
    // 添加部分关键词组合
    if (selectedKeywords.length > 2) {
      const partialQuery = selectedKeywords.slice(0, -1).map(k => `"${k}"`).join(' AND ');
      queries.push(`(${partialQuery})`);
    }
    
    const finalQuery = queries.join(' OR ');
    console.log(`简单查询长度: ${finalQuery.length} 字符`);
    
    return finalQuery;
  };

  // 智能查询生成器（自动选择最佳策略）
  const generateSmartQuery = (keywordList) => {
    if (keywordList.length === 0) return '';
    if (keywordList.length === 1) return `"${keywordList[0]}"`;

    // 根据关键词数量和长度选择策略
    const totalLength = keywordList.join('').length;
    const keywordCount = keywordList.length;
    
    let strategy;
    if (keywordCount <= 3 && totalLength <= 100) {
      strategy = 'field_weighted';
    } else if (keywordCount <= 6 && totalLength <= 200) {
      strategy = 'layered';
    } else {
      strategy = 'simple';
    }
    
    const query = generateWeightedQuery(keywordList, strategy);
    
    // 如果查询仍然太长，降级到更简单的策略
    if (query.length > 2000) {
      console.log('查询过长，降级到简单策略');
      return generateWeightedQuery(keywordList, 'simple');
    }
    
    console.log('=== 查询生成完成 ===');
    return query;
  };

  // 处理国家选择
  const handleCountrySelection = (country, isSelected, mode) => {
    if (mode === 'exclude') {
      const newExcluded = isSelected
        ? [...excludeCountries(), country]
        : excludeCountries().filter(c => c !== country);
      setExcludeCountries(newExcluded);
    } else if (mode === 'include') {
      const newIncluded = isSelected
        ? [...includeCountries(), country]
        : includeCountries().filter(c => c !== country);
      setIncludeCountries(newIncluded);
    }
  };

  return (
    <div class="container mx-auto p-6">
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title text-2xl mb-6">PubMed 审稿人查找系统</h2>
          
          {/* 文件上传区域 */}
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">上传 DOCX 文件（可选）</span>
            </label>
            <input 
              type="file" 
              accept=".docx"
              class="file-input file-input-bordered w-full"
              onChange={handleFileUpload}
            />
            <Show when={extractedContent()}>
              <div class="alert alert-success mt-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>文件解析成功！已自动提取标题和关键词</span>
              </div>
            </Show>
          </div>

          {/* 搜索表单 */}
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">研究关键词（用逗号分隔）</span>
              <span class="label-text-alt">例如：machine learning, artificial intelligence, deep learning</span>
            </label>
            
            {/* 关键词模式选择 */}
            <div class="flex flex-wrap gap-6 mb-2">
              <label class="label cursor-pointer">
                <input
                  type="radio"
                  name="keywordMode"
                  class="radio radio-primary radio-sm"
                  checked={keywordMode() === 'SMART'}
                  onChange={() => setKeywordMode('SMART')}
                />
                <span class="label-text ml-2 text-sm">智能组合（推荐）</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  type="radio"
                  name="keywordMode"
                  class="radio radio-primary radio-sm"
                  checked={keywordMode() === 'OR'}
                  onChange={() => setKeywordMode('OR')}
                />
                <span class="label-text ml-2 text-sm">OR模式</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  type="radio"
                  name="keywordMode"
                  class="radio radio-primary radio-sm"
                  checked={keywordMode() === 'AND'}
                  onChange={() => setKeywordMode('AND')}
                />
                <span class="label-text ml-2 text-sm">AND模式</span>
              </label>
            </div>
            
            <textarea
              placeholder={
                keywordMode() === 'SMART' 
                  ? "输入研究领域的关键词，用逗号分隔。智能组合模式将使用递减组合搜索，从最精确到最宽泛，获得最佳搜索结果"
                  : keywordMode() === 'OR' 
                    ? "输入研究领域的关键词，用逗号分隔。系统将搜索包含任一关键词的文献，以找到该领域的潜在审稿人"
                    : "输入研究领域的关键词，用逗号分隔。系统将搜索包含所有关键词的文献，搜索范围更精确"
              }
              class="textarea textarea-bordered w-full h-20"
              value={keywords()}
              onInput={(e) => setKeywords(e.target.value)}
            />
            
            {/* 显示当前查询策略示例 */}
            <Show when={keywords().trim() && keywordMode() === 'SMART'}>
              <div class="mt-2 p-2 bg-base-200 rounded text-xs">
                <strong>查询策略预览：</strong>
                <div class="mt-1 text-gray-600">
                  {(() => {
                    const keywordList = keywords().split(',').map(k => k.trim()).filter(k => k);
                    if (keywordList.length > 1) {
                      return generateSmartQuery(keywordList.slice(0, 3)); // 只显示前3个关键词的示例
                    }
                    return '';
                  })()}
                  {keywords().split(',').map(k => k.trim()).filter(k => k).length > 3 && '...'}
                </div>
              </div>
            </Show>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">初筛文章数量</span>
                <span class="label-text-alt">系统将自动分批处理，每批最多100篇</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={maxResults()}
                onChange={(e) => setMaxResults(parseInt(e.target.value))}
              >
                <option value={10}>10篇（调试开发）</option>
                <option value={50}>50篇（快速测试）</option>
                <option value={500}>500篇（推荐，请求快）</option>
                <option value={1000}>1000篇</option>
                <option value={2000}>2000篇</option>
                <option value={5000}>5000篇</option>
                <option value={9999}>9999篇（最大覆盖，请求慢）</option>
              </select>
              <div class="label">
                <span class="label-text-alt text-info">
                  💡 系统会先获取摘要进行预筛选，然后分批获取详细信息，避免请求过大
                </span>
              </div>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">排序方式</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={sortBy()}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="relevance">相关性优先</option>
                <option value="date">最新发表优先</option>
              </select>
            </div>
          </div>

          {/* 国家过滤选项 */}
          <div class="form-control mb-6">
            <label class="label">
              <span class="label-text">国家过滤（可选）</span>
              <span class="label-text-alt">过滤作者所在国家</span>
            </label>

            <div class="flex gap-4 mb-3">
              <label class="label cursor-pointer">
                <input
                  type="radio"
                  name="filterMode"
                  class="radio radio-primary"
                  checked={filterMode() === 'none'}
                  onChange={() => setFilterMode('none')}
                />
                <span class="label-text ml-2">不过滤</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  type="radio"
                  name="filterMode"
                  class="radio radio-primary"
                  checked={filterMode() === 'exclude'}
                  onChange={() => setFilterMode('exclude')}
                />
                <span class="label-text ml-2">排除国家（默认）</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  type="radio"
                  name="filterMode"
                  class="radio radio-primary"
                  checked={filterMode() === 'include'}
                  onChange={() => setFilterMode('include')}
                />
                <span class="label-text ml-2">仅包含国家</span>
              </label>
            </div>

            <Show when={filterMode() === 'exclude'}>
              <div class="border rounded-lg p-3 bg-base-200">
                <div class="text-sm font-medium mb-2">
                  选择要排除的国家：
                  <Show when={excludeCountries().length > 0}>
                    <span class="text-xs text-gray-500 ml-2">
                      (已选择 {excludeCountries().length} 个国家)
                    </span>
                  </Show>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-32 overflow-y-auto">
                  <For each={commonCountries}>
                    {(country) => (
                      <label class="label cursor-pointer justify-start">
                        <input
                          type="checkbox"
                          class="checkbox checkbox-sm"
                          checked={excludeCountries().includes(country)}
                          onChange={(e) => handleCountrySelection(country, e.target.checked, 'exclude')}
                        />
                        <span class="label-text ml-2 text-xs">{country}</span>
                      </label>
                    )}
                  </For>
                </div>
              </div>
            </Show>

            <Show when={filterMode() === 'include'}>
              <div class="border rounded-lg p-3 bg-base-200">
                <div class="text-sm font-medium mb-2">选择要包含的国家：</div>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-32 overflow-y-auto">
                  <For each={commonCountries}>
                    {(country) => (
                      <label class="label cursor-pointer justify-start">
                        <input
                          type="checkbox"
                          class="checkbox checkbox-sm"
                          checked={includeCountries().includes(country)}
                          onChange={(e) => handleCountrySelection(country, e.target.checked, 'include')}
                        />
                        <span class="label-text ml-2 text-xs">{country}</span>
                      </label>
                    )}
                  </For>
                </div>
              </div>
            </Show>
          </div>

          {/* 搜索按钮 */}
          <div class="card-actions justify-end">
            <button 
              class="btn btn-primary"
              onClick={performSearch}
              disabled={isSearching()}
            >
              <Show when={isSearching()}>
                <span class="loading loading-spinner loading-sm"></span>
              </Show>
              {isSearching() ? '搜索审稿人中...' : '查找审稿人'}
            </button>
          </div>

          {/* 搜索进度条 */}
          <Show when={searchProgress().isVisible}>
            <div class="mt-4">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">{searchProgress().stage}</span>
                <span class="text-sm text-gray-500">
                  {searchProgress().completed}/{searchProgress().total}
                </span>
              </div>
              <progress
                class="progress progress-primary w-full"
                value={searchProgress().completed}
                max={searchProgress().total}
              ></progress>
            </div>
          </Show>

          {/* 错误信息 */}
          <Show when={error()}>
            <div class="alert alert-error mt-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error()}</span>
            </div>
          </Show>
        </div>
      </div>

      {/* 搜索结果 */}
      <Show when={searchResults().length > 0}>
        <div class="mt-6">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h3 class="text-xl font-bold">找到 {searchResults().length} 篇相关文章</h3>
              <div class="text-sm text-gray-500 mt-1">
                总作者数: {searchResults().reduce((total, article) => total + article.authors.length, 0)} |
                有ORCID: {searchResults().reduce((total, article) => total + article.authors.filter(a => a.orcid).length, 0)} |
                有邮箱: {searchResults().reduce((total, article) => total + article.authors.filter(a => a.email).length, 0)}
              </div>
            </div>
            <div class="flex gap-2">
              <button
                class="btn btn-sm btn-outline"
                onClick={toggleSelectAll}
              >
                {selectedAuthors().size > 0 ? '取消全选' : '全选审稿人'}
              </button>
              <button
                class="btn btn-sm btn-primary"
                onClick={exportSelectedReviewers}
                disabled={selectedAuthors().size === 0}
              >
                导出选中审稿人 ({selectedAuthors().size})
              </button>
            </div>
          </div>
          <For each={searchResults()}>
            {(article, index) => (
              <div class="card bg-base-100 shadow-lg mb-4">
                <div class="card-body">
                  <div class="flex justify-between items-start mb-2">
                    <h4 class="card-title text-lg w-[70%]">{article.title}</h4>
                    <div class="flex gap-2 flex-wrap">
                      <Show when={article.comprehensiveHitRate !== undefined}>
                        <div class="badge badge-primary">
                          综合命中率: {(article.comprehensiveHitRate * 100).toFixed(1)}%
                        </div>
                      </Show>
                      <Show when={article.hitRate !== undefined}>
                        <div class="badge badge-success">
                          个数命中率: {(article.hitRate * 100).toFixed(0)}%
                        </div>
                      </Show>
                      <Show when={article.hitCount !== undefined}>
                        <div class="badge badge-info">
                          命中: {article.hitCount}/{keywords().split(',').filter(k => k.trim()).length}
                        </div>
                      </Show>
                    </div>
                  </div>
                  
                  <div class="text-sm text-gray-600 mb-2">
                    <strong>期刊:</strong> {article.journal} | 
                    <strong> 发表日期:</strong> {article.pubdate} |
                    <strong> PMID:</strong> 
                    <a 
                      href={`https://pubmed.ncbi.nlm.nih.gov/${article.pmid}/`}
                      target="_blank"
                      class="link link-primary ml-1"
                    >
                      {article.pmid}
                    </a>
                    <Show when={article.doi}>
                      | <strong>DOI:</strong> 
                      <a 
                        href={`https://doi.org/${article.doi}`}
                        target="_blank"
                        class="link link-primary ml-1"
                      >
                        {article.doi}
                      </a>
                    </Show>
                  </div>

                  <Show when={article.abstract}>
                    <div class="mb-4">
                      <strong>摘要:</strong>
                      <p class="text-sm mt-1">{article.abstract}</p>
                    </div>
                  </Show>

                  {/* 潜在审稿人信息 */}
                  <div class="mb-4">
                    <strong>潜在审稿人:</strong>
                    <div class="overflow-x-auto mt-2">
                      <table class="table table-compact w-full">
                        <thead>
                          <tr>
                            <th>选择</th>
                            <th>姓名</th>
                            <th>机构</th>
                            <th>国家</th>
                            <th>ORCID</th>
                            <th>邮箱</th>
                            <th>外部链接</th>
                          </tr>
                        </thead>
                        <tbody>
                          <For each={article.authors}>
                            {(author, authorIndex) => {
                              const links = getAuthorLinks(author);
                              const authorKey = `${index()}-${authorIndex()}`;
                              return (
                                <tr class={selectedAuthors().has(authorKey) ? 'bg-primary/10' : ''}>
                                  <td>
                                    <input
                                      type="checkbox"
                                      class="checkbox checkbox-sm"
                                      checked={selectedAuthors().has(authorKey)}
                                      onChange={() => toggleAuthorSelection(authorKey)}
                                    />
                                  </td>
                                  <td class="font-medium">{author.forename} {author.lastname}</td>
                                  <td class="text-xs max-w-xs truncate" title={author.institution || author.affiliation || ''}>
                                    {author.institution || author.affiliation || '-'}
                                  </td>
                                  <td>{author.country || '-'}</td>
                                  <td>
                                    <Show when={author.orcid} fallback="-">
                                      <a
                                        href={`https://orcid.org/${author.orcid}`}
                                        target="_blank"
                                        class="link link-primary text-xs"
                                      >
                                        {author.orcid}
                                      </a>
                                    </Show>
                                  </td>
                                  <td>
                                    <Show when={author.email} fallback="-">
                                      <a href={`mailto:${author.email}`} class="link link-primary text-xs">
                                        {author.email}
                                      </a>
                                    </Show>
                                  </td>
                                  <td>
                                    <div class="flex gap-1">
                                      <a
                                        href={links.researchgate}
                                        target="_blank"
                                        class="btn btn-xs btn-outline"
                                        title="ResearchGate搜索"
                                      >
                                        RG
                                      </a>
                                      <a
                                        href={links.linkedin}
                                        target="_blank"
                                        class="btn btn-xs btn-outline"
                                        title="LinkedIn搜索"
                                      >
                                        LI
                                      </a>
                                    </div>
                                  </td>
                                </tr>
                              );
                            }}
                          </For>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <Show when={article.keywords && article.keywords.length > 0}>
                    <div class="mb-2">
                      <strong>关键词:</strong>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <For each={article.keywords}>
                          {(keyword) => (
                            <span class="badge badge-outline badge-sm">{keyword}</span>
                          )}
                        </For>
                      </div>
                    </div>
                  </Show>

                  <Show when={article.keywordMatches && article.keywordMatches.length > 0}>
                    <div>
                      <strong>关键词匹配:</strong>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <For each={article.keywordMatches.filter(match => match.score > 0)}>
                          {(match) => (
                            <span class={`badge badge-sm ${
                              match.type === 'exact' ? 'badge-success' : 
                              match.type === 'partial' ? 'badge-warning' : 'badge-info'
                            }`} title={`相似率: ${(match.score * 100).toFixed(1)}%`}>
                              {match.keyword} ({(match.score * 100).toFixed(1)}%)
                            </span>
                          )}
                        </For>
                      </div>
                      <Show when={article.comprehensiveHitRate !== undefined && article.keywordMatches.filter(m => m.score > 0).length > 1}>
                        <div class="text-xs text-gray-500 mt-1">
                          综合命中率计算: {(article.hitRate * 100).toFixed(0)}% × {
                            article.keywordMatches
                              .filter(m => m.score > 0)
                              .map(m => `${(m.score * 100).toFixed(1)}%`)
                              .join(' × ')
                          } = {(article.comprehensiveHitRate * 100).toFixed(1)}%
                        </div>
                      </Show>
                    </div>
                  </Show>
                </div>
              </div>
            )}
          </For>
        </div>
      </Show>
    </div>
  );
};

export default PubMedSearch;
