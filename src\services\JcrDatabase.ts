import initSqlJs, { Database } from 'sql.js';

export class JcrDatabase {
    private db: Database | null = null;

    async init(dbPath: string): Promise<void> {
        try {
            const SQL = await initSqlJs({
                locateFile: file => chrome.runtime.getURL(`assets/${file}`),
                wasmBinary: await (await fetch(chrome.runtime.getURL('assets/sql-wasm.wasm'))).arrayBuffer()
            });

            const dbResponse = await fetch(dbPath);
            const dbBuffer = await dbResponse.arrayBuffer();
            this.db = new SQL.Database(new Uint8Array(dbBuffer));
        } catch (error) {
            console.error('Failed to load JCR database:', error);
            throw error;
        }
    }

    getJournalQuartile(issn: string): string {



        if (!this.db) {
            throw new Error('Database not initialized');
        }

        try {
            console.log(issn)
            const contents = this.db?.exec(`select * from jcr2023 where eissn="${issn}" LIMIT 1`)
            console.log(contents)
            if (contents && contents.length==1) {
                const { columns, values } = contents[0]
                return String(values[0][7])
            }
            return 'Unknown';
        } catch (error) {
            console.error('Error querying journal quartile:', error);
            return 'Unknown';
        }
    }

    close(): void {
        if (this.db) {
            this.db.close();
        }
    }
}

export const jcrDatabase = new JcrDatabase();


