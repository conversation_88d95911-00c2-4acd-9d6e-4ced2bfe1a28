import HttpClient from "./http_client";

// 请求拦截
HttpClient.prototype.request = function (option) {
    option.url = `/${option.url}`;
    option.headers = {
        "ameAuthorization": localStorage.token,
        ...option.headers
    }
};

// 响应拦截
HttpClient.prototype.response = function (res) {
    try {
        let data = JSON.parse(res);
        return Promise.resolve(data)
    } catch (e) {
        return Promise.resolve(res)
    }

};
// 错误响应拦截
HttpClient.prototype.responseError = function (err) {
    return Promise.reject(err)
};

const ossHttp = new HttpClient("api/oss");
const vodHttp = new HttpClient("/api/vodapi");


export {
    ossHttp,
    vodHttp
}
