import { createSignal, Show, JSX, onMount } from 'solid-js';
import { Api } from "../restful/api/restApi";
import QimsItemImpl from "./qimsRefItem";
import ParseDocFile from "./ParseDocFile";
import ParseXmlFile from "./ParseXmlFile";

const ReferenceList = (): JSX.Element => {
  const [showMask, setShowMask] = createSignal(false);
  const [current, setCurrent] = createSignal(1);
  const [total, setTotal] = createSignal(0);
  const [state, setState] = createSignal({ select: 1, wordSelect: 5 });
  let selectFiles: FileList;

  const changeFileSelector = (value: number) => {
    const fileDom = document.querySelectorAll('#view1 .file_segment');
    fileDom.forEach(item => item.className = 'file_segment');
    const currentDom = document.getElementById('File_' + value);
    if (currentDom) currentDom.className = 'file_segment active';
  };

  const updateView = (files: FileList) => {
    if (!files.length) {
      const elem = document.getElementById("view1");
      if (elem) elem.innerText = '';
      setShowMask(false);
      return;
    } else {
      const elem = document.getElementById("view1");
      if (elem) elem.innerText = '';
      setShowMask(true);
      setTotal(files.length);
      setCurrent(1);
    }
  };

  function removeBorderStyles(element: HTMLElement) {
    // 移除当前元素的 border 样式
    if (element.style) {
      element.style.border = 'none';
      element.style.borderTop = 'none';
      element.style.borderRight = 'none';
      element.style.borderBottom = 'none';
      element.style.borderLeft = 'none';
    }

    // 递归处理子元素
    for (let i = 0; i < element.children.length; i++) {
      removeBorderStyles(element.children[i] as HTMLElement);
    }
  }

  function copyToClipboard(html: string) {
    console.log(html)
    // 创建一个隐藏的可编辑容器
    const container = document.createElement('div');
    container.innerHTML = html;
    container.style.position = 'fixed';
    container.style.pointerEvents = 'none';
    container.style.opacity = '0';
    removeBorderStyles(container);
    document.body.appendChild(container);

    // 选择容器内容
    window.getSelection()?.removeAllRanges();
    const range = document.createRange();
    range.selectNode(container);
    window.getSelection()?.addRange(range);

    try {
      // 执行复制命令
      document.execCommand('copy');
      alert('已复制到剪贴板');
    } catch (err) {
      alert('复制失败');
    } finally {
      // 清理
      window.getSelection()?.removeAllRanges();
      document.body.removeChild(container);
    }
  }

  const ReadLocaleFiles = (inputElement: HTMLInputElement) => {
    selectFiles = inputElement.files;
    const files = inputElement.files || [];
    updateView(files);
    if (!files.length) return;
    const selector = document.getElementById('convertFile_type');
    let parseFile = new ParseXmlFile(state().select, selectFiles);
    parseFile.readFiles(selector).then(res => {
      handleResult(res, files, parseFile.zip, 'xml');
    });
  };

  const ReadWordFiles = (inputElement: HTMLInputElement) => {
    selectFiles = inputElement.files;
    const files = inputElement.files || [];
    console.log(files)
    updateView(files);
    if (!files.length) return;
    const selector = document.getElementById('convertFile_type');
    let parseFile = new ParseDocFile(state().wordSelect, selectFiles, selector, (res, zip) => {
      handleResult(res, files, zip, 'doc');
    });
    parseFile.readFiles();
  };

  const handleResult = (res: any, files: FileList, zip: any, fileType: string) => {
    const downloadRow = document.getElementById("download_row");
    const convertFileType = document.getElementById("convertFile_type");
    if (downloadRow) downloadRow.style.display = "flex";
    if (files.length > 1 && convertFileType) convertFileType.style.display = "inline-block";
    if (!res) {
      setShowMask(false);
      if (downloadRow) downloadRow.style.display = "none";
      if (convertFileType) convertFileType.style.display = "none";
      const elem = document.getElementById("view1");
      if (elem) elem.innerHTML = '文件无具体转换内容,请换个文件操作';
    } else {
      setShowMask(false);
      const download = document.getElementById("download");
      if (download) download.addEventListener("click", () => Export2Word(zip, fileType));
    }
  };

  const combineHTMLContent = (html: string) => {
    const preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";
    const postHtml = "</body></html>";
    return preHtml + html + postHtml;
  };

  const Export2Word = (zip: any, type: string) => {
    if (selectFiles.length > 1) {
      DownLoadZIP(zip);
    } else {
      const html = combineHTMLContent(document.getElementById('File_0')?.innerHTML || "");
      const blob = new Blob(['\ufeff', html], { type: 'application/msword' });
      const url = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(html);
      const replacePattern = type === 'doc' ? /\.(doc|docx)/i : /\.xml/i;
      let filename = selectFiles[0].name.replace(replacePattern, "");
      filename = filename ? filename + '.doc' : 'document.doc';
      const downloadLink = document.createElement("a");
      document.body.appendChild(downloadLink);
      if (navigator.msSaveOrOpenBlob) {
        navigator.msSaveOrOpenBlob(blob, filename);
      } else {
        downloadLink.href = url;
        downloadLink.download = filename;
        downloadLink.click();
      }
      document.body.removeChild(downloadLink);
    }
  };

  const DownLoadZIP = (zip: any) => {
    zip.generateAsync({ type: "blob" }).then((blob: Blob) => {
      const a = document.createElement('a');
      a.download = `${new Date().toLocaleString()}.zip`;
      a.href = URL.createObjectURL(blob);
      a.dataset.downloadurl = ["text/html", a.download, a.href].join(':');
      a.style.display = "none";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setTimeout(() => {
        URL.revokeObjectURL(a.href);
      }, 1500);
    });
  };

  const searchPmid = async (pmid: string) => {
    const refIdList = [{ rId: "1", pmid: pmid }];
    await getSummary(pmid).then(res => {
      const refListImpl = new QimsItemImpl(null, 0, refIdList, res, pmid);
      refListImpl.render().then(async html => {
        // 渲染逻辑
      }, rej => {
        const elem = document.getElementById('view1');
        if (elem) elem.innerText = rej || '';
      });
    }, rej => {
      const elem = document.getElementById('view1');
      if (elem) elem.innerText = rej || '';
    });
  };

  const getSummary = (ids: string): Promise<string> => {
    return Api.get("/entrez/eutils/esummary.fcgi", { db: "pubmed", id: ids }, {}).then(res => {
      return res;
    });
  };

  onMount(() => {
    document.getElementById("xml_type")?.addEventListener('change', (event: any) => {
      setState({ ...state(), select: parseInt(event.target.value || 0) });
    });
    document.getElementById("word_type")?.addEventListener('change', (event: any) => {
      setState({ ...state(), wordSelect: parseInt(event.target.value || 0) });
    });
    document.getElementById('convertFile_type')?.addEventListener('change', (e: any) => {
      const value = e.target.value;
      changeFileSelector(value);
    });
    document.getElementById("file")?.addEventListener("change", function () {
      ReadLocaleFiles(this);
    });
    document.getElementById("wordFile")?.addEventListener("change", function () {
      ReadWordFiles(this);
    });
    document.getElementById('pmidSearch')?.addEventListener('click', () => {
      const pmid = (document.getElementById('pmidInput') as HTMLInputElement).value;
      if (!pmid) return;
      const elem = document.getElementById("view1");
      if (elem) elem.innerText = '';
      searchPmid(pmid);
    });
  });

  return (
    <div class="flex h-screen">
      <div class="flex-1 flex flex-col">
        <div class="flex-1 p-4 overflow-auto">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">选择 XML 类型</span>
              </label>
              <select class="select select-bordered" id="xml_type">
                <option value="1">普通杂志-xml</option>
                <option value="2">qims杂志-xml</option>
                <option value="3">中文杂志-xml</option>
                <option value="4">CJTS中文杂志-xml</option>
              </select>
              <input
                id="file"
                type="file"
                class="file-input file-input-bordered w-full mt-2"
                multiple
                accept="text/xml,application/xml"
              />
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">选择 Word 类型</span>
              </label>
              <select class="select select-bordered" id="word_type">
                <option value="5">普通杂志-word</option>
                <option value="6">qims杂志-word</option>
                <option value="7">中文杂志-word</option>
                <option value="8">CJTS杂志-word</option>
                <option value="9">中文书文献-word</option>
              </select>
              <input
                id="wordFile"
                type="file"
                class="file-input file-input-bordered w-full mt-2"
                multiple
                accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              />
            </div>
          </div>
          <div class="divider my-4"></div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">输入 PMID</span>
              </label>
              <input
                id="pmidInput"
                type="number"
                class="input input-bordered w-full"
                placeholder="请输入pmid(qims)"
              />
              <button
                id="pmidSearch"
                class="btn btn-primary mt-2"
              >
                Search
              </button>
            </div>
            <div class="form-control" id="download_row"
            style="display: none;"
            >
              <button
                id="download"
                class="btn btn-secondary mt-8"
              >
                DownLoad Word
              </button>
              <select
                id="convertFile_type"
                class="select select-bordered mt-2"
                style="display: none;"
              >
              </select>
              <div class="flex items-center gap-2">
                <button
                  onClick={() => copyToClipboard(document.getElementById('view1')?.innerHTML || '')}
                  class="btn btn-success mt-2 text-white w-full"
                  title="复制内容"
                >
                  复制
                </button>
              </div>
            </div>
          </div>
          <div class="divider my-4"></div>
          <div id="view1" class="overflow-auto text-xl"></div>
          <div id="view2" class="overflow-auto text-xl"></div>
          <div id="view3" class="overflow-auto text-xl"></div>
          <Show when={showMask()}>
            <div id="mask" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div class="text-white text-center">
                <p>
                  正在处理第 <span id="current">{current()}</span>个，
                  共计<span id="count">{total()}</span>个
                </p>
                <p>正在处理文件...</p>
              </div>
            </div>
          </Show>
        </div>
      </div>
    </div>
  );
};

export default ReferenceList;
