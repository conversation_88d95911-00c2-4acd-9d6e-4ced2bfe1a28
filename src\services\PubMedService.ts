/**
 * PubMed API 服务
 * 提供 PubMed 文献搜索和详细信息获取功能
 */

export class PubMedService {
  private static readonly BASE_URL = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/';
  private static readonly SEARCH_URL = `${PubMedService.BASE_URL}esearch.fcgi`;
  private static readonly FETCH_URL = `${PubMedService.BASE_URL}efetch.fcgi`;
  private static readonly SUMMARY_URL = `${PubMedService.BASE_URL}esummary.fcgi`;

  /**
   * 从机构信息中提取邮箱地址
   */
  private static extractEmailFromAffiliation(affiliation: string): string | undefined {
    if (!affiliation) return undefined;
    
    // 匹配邮箱地址的正则表达式
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const matches = affiliation.match(emailRegex);
    
    if (matches && matches.length > 0) {
      // 返回第一个找到的邮箱地址
      return matches[0];
    }
    
    return undefined;
  }

  /**
   * 搜索 PubMed 文献
   */
  static async searchArticles(params: PubMedSearchParams): Promise<PubMedSearchResult> {
    const searchData = {
      db: 'pubmed',
      term: params.query,
      retmode: 'json',
      retmax: (params.maxResults || 9999).toString(),
      sort: params.sort === 'date' ? 'pub_date' : 'relevance'
    };

    // 检查查询长度，决定使用GET还是POST
    const queryLength = params.query.length;
    const usePost = queryLength > 2000; // URL长度限制通常在2048字符左右

    console.log(`查询长度: ${queryLength} 字符，使用 ${usePost ? 'POST' : 'GET'} 请求`);

    try {
      let response;
      
      if (usePost) {
        // 使用POST请求避免URL长度限制
        const formData = new URLSearchParams(searchData);
        
        response = await fetch(this.SEARCH_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
          },
          body: formData.toString()
        });
      } else {
        // 使用GET请求（短查询）
        const searchParams = new URLSearchParams(searchData);
        
        response = await fetch(`${this.SEARCH_URL}?${searchParams}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
          }
        });
      }

      if (!response.ok) {
        throw new Error(`PubMed search failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.esearchresult;
    } catch (error) {
      console.error('PubMed search error:', error);
      throw new Error('Failed to search PubMed articles');
    }
  }

  /**
   * 获取文章详细信息
   */
  static async getArticleDetails(pmids: string[]): Promise<PubMedDetailResponse> {
    const fetchParams = new URLSearchParams({
      db: 'pubmed',
      id: pmids.join(','),
      retmode: 'json',
      rettype: 'abstract'
    });

    try {
      const response = await fetch(`${this.SUMMARY_URL}?${fetchParams}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
        }
      });

      if (!response.ok) {
        throw new Error(`PubMed fetch failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('PubMed fetch error:', error);
      throw new Error('Failed to fetch article details');
    }
  }

  /**
   * 获取文章的完整信息（包括摘要）
   */
  static async getFullArticleInfo(pmids: string[]): Promise<string> {
    const fetchParams = new URLSearchParams({
      db: 'pubmed',
      id: pmids.join(','),
      retmode: 'xml',
      rettype: 'abstract'
    });

    try {
      const response = await fetch(`${this.FETCH_URL}?${fetchParams}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/xml',
          'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
        }
      });

      if (!response.ok) {
        throw new Error(`PubMed fetch failed: ${response.statusText}`);
      }

      return await response.text();
    } catch (error) {
      console.error('PubMed fetch error:', error);
      throw new Error('Failed to fetch full article info');
    }
  }

  /**
   * 解析 XML 响应并提取文章信息
   */
  static parseArticleXML(xmlText: string): PubMedArticle[] {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
    const articles: PubMedArticle[] = [];

    const pubmedArticles = xmlDoc.querySelectorAll('PubmedArticle');
    
    pubmedArticles.forEach(articleNode => {
      const pmid = articleNode.querySelector('PMID')?.textContent || '';
      const titleNode = articleNode.querySelector('ArticleTitle');
      const title = titleNode?.textContent || '';
      
      // 提取摘要
      const abstractNodes = articleNode.querySelectorAll('AbstractText');
      let abstract = '';
      abstractNodes.forEach(node => {
        if (abstract) abstract += ' ';
        abstract += node.textContent || '';
      });

      // 提取作者信息
      const authors: PubMedAuthor[] = [];
      const authorNodes = articleNode.querySelectorAll('Author');
      
      authorNodes.forEach(authorNode => {
        const lastname = authorNode.querySelector('LastName')?.textContent || '';
        const forename = authorNode.querySelector('ForeName')?.textContent || '';
        const initials = authorNode.querySelector('Initials')?.textContent || '';
        
        // 提取机构信息
        const affiliationNode = authorNode.querySelector('AffiliationInfo Affiliation');
        const affiliation = affiliationNode?.textContent || '';
        
        // 从机构信息中提取邮箱地址
        const email = this.extractEmailFromAffiliation(affiliation);
        
        // 提取 ORCID（如果有）
        const orcidNode = authorNode.querySelector('Identifier[Source="ORCID"]');
        const orcid = orcidNode?.textContent || '';

        if (lastname || forename) {
          authors.push({
            lastname,
            forename,
            initials,
            affiliation,
            email: email || undefined,
            orcid: orcid || undefined
          });
        }
      });

      // 提取期刊信息
      const journalNode = articleNode.querySelector('Journal Title');
      const journal = journalNode?.textContent || '';

      // 提取发表日期
      const pubDateNode = articleNode.querySelector('PubDate');
      let pubdate = '';
      if (pubDateNode) {
        const year = pubDateNode.querySelector('Year')?.textContent || '';
        const month = pubDateNode.querySelector('Month')?.textContent || '';
        const day = pubDateNode.querySelector('Day')?.textContent || '';
        pubdate = [year, month, day].filter(Boolean).join('-');
      }

      // 提取 DOI
      const doiNode = articleNode.querySelector('ArticleId[IdType="doi"]');
      const doi = doiNode?.textContent || '';

      // 提取关键词
      const keywords: string[] = [];
      const keywordNodes = articleNode.querySelectorAll('Keyword');
      keywordNodes.forEach(node => {
        const keyword = node.textContent?.trim();
        if (keyword) keywords.push(keyword);
      });

      // 提取机构信息
      const affiliations: string[] = [];
      const affiliationNodes = articleNode.querySelectorAll('AffiliationInfo Affiliation');
      affiliationNodes.forEach(node => {
        const affiliation = node.textContent?.trim();
        if (affiliation && !affiliations.includes(affiliation)) {
          affiliations.push(affiliation);
        }
      });

      if (pmid && title) {
        articles.push({
          pmid,
          title,
          abstract: abstract || undefined,
          authors,
          journal,
          pubdate,
          doi: doi || undefined,
          keywords: keywords.length > 0 ? keywords : undefined,
          affiliations: affiliations.length > 0 ? affiliations : undefined
        });
      }
    });

    return articles;
  }

  /**
   * 分批获取文章信息
   */
  static async fetchArticlesBatch(pmids: string[], batchSize: number = 100): Promise<PubMedArticle[]> {
    const allArticles: PubMedArticle[] = [];

    for (let i = 0; i < pmids.length; i += batchSize) {
      const batch = pmids.slice(i, i + batchSize);

      try {
        const xmlResponse = await this.getFullArticleInfo(batch);
        const articles = this.parseArticleXML(xmlResponse);
        allArticles.push(...articles);

        // 添加小延迟避免请求过快
        if (i + batchSize < pmids.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      } catch (error) {
        console.error(`Error fetching batch ${i}-${i + batchSize}:`, error);
        // 继续处理下一批，不中断整个流程
      }
    }

    return allArticles;
  }

  /**
   * 获取文章摘要信息（用于初步筛选）
   */
  static async getArticleSummaries(pmids: string[]): Promise<any[]> {
    const summaryParams = new URLSearchParams({
      db: 'pubmed',
      id: pmids.join(','),
      retmode: 'json'
    });

    try {
      const response = await fetch(`${this.SUMMARY_URL}?${summaryParams}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; Chrome Extension)'
        }
      });

      if (!response.ok) {
        throw new Error(`PubMed summary fetch failed: ${response.statusText}`);
      }

      const data = await response.json();
      return Object.values(data.result || {}).filter(item => item && typeof item === 'object');
    } catch (error) {
      console.error('PubMed summary fetch error:', error);
      return [];
    }
  }

  /**
   * 智能搜索并获取文章信息（支持预筛选和分批处理）
   */
  static async searchAndFetchArticles(params: PubMedSearchParams, progressCallback?: (current: number, total: number, stage: string) => void): Promise<PubMedArticle[]> {
    try {
      // 步骤1: 搜索获取 PMID 列表
      progressCallback?.(0, 4, '正在搜索 PubMed 文献...');
      const searchResult = await this.searchArticles(params);

      if (!searchResult.idlist || searchResult.idlist.length === 0) {
        return [];
      }

      progressCallback?.(1, 4, `找到 ${searchResult.idlist.length} 篇文章，开始获取摘要信息...`);

      // 步骤2: 分批获取摘要信息用于预筛选
      const batchSize = 100;
      const allSummaries: any[] = [];

      for (let i = 0; i < searchResult.idlist.length; i += batchSize) {
        const batch = searchResult.idlist.slice(i, i + batchSize);
        const currentBatch = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(searchResult.idlist.length / batchSize);

        progressCallback?.(1, 4, `正在获取第 ${currentBatch}/${totalBatches} 批摘要信息...`);

        try {
          const summaries = await this.getArticleSummaries(batch);
          allSummaries.push(...summaries);

          // 添加小延迟避免请求过快
          if (i + batchSize < searchResult.idlist.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        } catch (error) {
          console.error(`Error fetching summary batch ${currentBatch}:`, error);
        }
      }

      progressCallback?.(2, 4, `获得 ${allSummaries.length} 篇文章摘要，开始预筛选...`);

      // 步骤3: 预筛选最相关的文章（限制为200篇以内）
      const keywords = params.keywords || [];
      const filteredSummaries = allSummaries.slice(0, 200); // 简单取前200篇，后续可以加入更智能的筛选

      if (filteredSummaries.length === 0) {
        return [];
      }

      progressCallback?.(3, 4, `预筛选出 ${filteredSummaries.length} 篇文章，开始获取详细信息...`);

      // 步骤4: 获取筛选后文章的详细信息
      const selectedPmids = filteredSummaries.map(summary => summary.uid).filter(Boolean);
      const allArticles: PubMedArticle[] = [];

      for (let i = 0; i < selectedPmids.length; i += batchSize) {
        const batch = selectedPmids.slice(i, i + batchSize);
        const currentBatch = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(selectedPmids.length / batchSize);

        progressCallback?.(3, 4, `正在获取第 ${currentBatch}/${totalBatches} 批详细信息...`);

        try {
          const xmlResponse = await this.getFullArticleInfo(batch);
          const articles = this.parseArticleXML(xmlResponse);
          allArticles.push(...articles);

          // 添加小延迟避免请求过快
          if (i + batchSize < selectedPmids.length) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        } catch (error) {
          console.error(`Error fetching detail batch ${currentBatch}:`, error);
        }
      }

      progressCallback?.(4, 4, `成功获取 ${allArticles.length} 篇文章详情`);
      return allArticles;
    } catch (error) {
      console.error('Error in searchAndFetchArticles:', error);
      throw error;
    }
  }
}
