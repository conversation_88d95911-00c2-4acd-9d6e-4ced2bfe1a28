import { createSignal, createEffect } from 'solid-js';

interface CSVData {
    编号: string;
    稿号: string;
}

interface FileInfo {
    path: string;
    name: string;
    handle: FileSystemFileHandle;
    parentDir: FileSystemDirectoryHandle;
}

interface RenamePreview {
    originalPath: string;
    newPath: string;
    matched: boolean;
    编号?: string;
    稿号?: string;
}

interface RenameResult {
    success: boolean;
    originalPath: string;
    newPath?: string;
    error?: string;
    skipped?: boolean;
}

export default function CSVBatchRename() {
    const [csvData, setCSVData] = createSignal<CSVData[]>([]);
    const [csvFile, setCSVFile] = createSignal<File | null>(null);
    const [files, setFiles] = createSignal<FileInfo[]>([]);
    const [previewResults, setPreviewResults] = createSignal<RenamePreview[]>([]);
    const [processing, setProcessing] = createSignal(false);
    const [rootDirectoryHandle, setRootDirectoryHandle] = createSignal<FileSystemDirectoryHandle | null>(null);
    const [includeSubfolders, setIncludeSubfolders] = createSignal(false);
    const [allowOverwrite, setAllowOverwrite] = createSignal(true);

    // CSV文件解析函数
    const parseCSV = async (file: File): Promise<CSVData[]> => {
        const text = await file.text();
        const lines = text.split('\n').filter(line => line.trim());
        
        if (lines.length < 2) {
            throw new Error('CSV文件至少需要包含标题行和一行数据');
        }

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const 编号Index = headers.findIndex(h => h.includes('编号') || h.toLowerCase().includes('number'));
        const 稿号Index = headers.findIndex(h => h.includes('稿号') || h.toLowerCase().includes('code'));

        if (编号Index === -1 || 稿号Index === -1) {
            throw new Error('CSV文件必须包含"编号"和"稿号"列');
        }

        const data: CSVData[] = [];
        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
            if (values.length > Math.max(编号Index, 稿号Index)) {
                data.push({
                    编号: values[编号Index],
                    稿号: values[稿号Index]
                });
            }
        }

        return data;
    };

    // 处理CSV文件上传
    const handleCSVUpload = async (event: Event) => {
        const input = event.target as HTMLInputElement;
        const file = input.files?.[0];
        
        if (!file) return;

        try {
            setProcessing(true);
            const data = await parseCSV(file);
            setCSVData(data);
            setCSVFile(file);
            console.log('CSV数据解析成功:', data);
        } catch (error) {
            alert(`CSV文件解析失败: ${(error as Error).message}`);
            input.value = '';
        } finally {
            setProcessing(false);
        }
    };

    // 处理目录选择
    const processDirectory = async (dirHandle: FileSystemDirectoryHandle, currentPath = ''): Promise<FileInfo[]> => {
        const fileInfos: FileInfo[] = [];
        try {
            for await (const entry of dirHandle.values()) {
                const entryPath = currentPath ? `${currentPath}/${entry.name}` : entry.name;
                if (entry.kind === 'file' && entry.name.toLowerCase().endsWith('.indd')) {
                    fileInfos.push({
                        path: entryPath,
                        name: entry.name,
                        handle: entry as FileSystemFileHandle,
                        parentDir: dirHandle
                    });
                } else if (entry.kind === 'directory' && includeSubfolders()) {
                    try {
                        const subDirHandle = await dirHandle.getDirectoryHandle(entry.name);
                        const subFiles = await processDirectory(subDirHandle, entryPath);
                        fileInfos.push(...subFiles);
                    } catch (subDirError: any) {
                        console.warn(`无法访问子目录 "${entryPath}": ${subDirError.message}`);
                    }
                }
            }
        } catch (error: any) {
            console.error(`处理目录 "${currentPath || dirHandle.name}" 时出错: ${error.message}`);
        }
        return fileInfos;
    };

    const handleDirectorySelect = async () => {
        try {
            const dirHandle = await window.showDirectoryPicker();
            setRootDirectoryHandle(dirHandle);
            setProcessing(true);
            const fileInfos = await processDirectory(dirHandle, '');
            setFiles(fileInfos);
            if (fileInfos.length === 0) {
                alert("选择的目录中未找到.indd文件");
            }
        } catch (error) {
            if (error instanceof DOMException && error.name === 'AbortError') {
                console.log('目录选择被用户取消');
            } else {
                console.error('选择目录时出错:', error);
                alert(`选择目录失败: ${(error as Error).message || '未知错误'}`);
            }
        } finally {
            setProcessing(false);
        }
    };

    // 生成重命名预览
    const generatePreview = () => {
        const currentFiles = files();
        const currentCSVData = csvData();
        const results: RenamePreview[] = [];

        currentFiles.forEach(file => {
            // 从文件名中提取稿号（去掉扩展名和可能的后缀）
            const nameWithoutExt = file.name.replace(/\.indd$/i, '');

            // 查找匹配的CSV数据 - 使用更精确的匹配逻辑
            const matchedData = currentCSVData.find(data => {
                // 检查稿号是否在文件名中，并且稿号不为空
                if (!data.稿号 || !data.稿号.trim()) return false;

                // 精确匹配：稿号应该作为完整的部分出现在文件名中
                const 稿号Pattern = data.稿号.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义特殊字符
                const regex = new RegExp(`\\b${稿号Pattern}\\b|${稿号Pattern}(?=[-_.]|$)`, 'i');
                return regex.test(nameWithoutExt);
            });

            if (matchedData) {
                // 生成新文件名：编号-原文件名
                const newName = `${matchedData.编号}-${file.name}`;
                const pathSegments = file.path.split('/');
                pathSegments.pop();
                const basePath = pathSegments.join('/');
                const newPath = basePath ? `${basePath}/${newName}` : newName;

                results.push({
                    originalPath: file.path,
                    newPath: newPath,
                    matched: true,
                    编号: matchedData.编号,
                    稿号: matchedData.稿号
                });
            } else {
                results.push({
                    originalPath: file.path,
                    newPath: file.path,
                    matched: false
                });
            }
        });

        setPreviewResults(results);
    };

    // 执行重命名操作
    const executeRename = async (): Promise<RenameResult[]> => {
        const results: RenameResult[] = [];
        const previews = previewResults().filter(p => p.matched && p.originalPath !== p.newPath);

        for (const preview of previews) {
            const file = files().find(f => f.path === preview.originalPath);
            if (!file) {
                results.push({
                    success: false,
                    originalPath: preview.originalPath,
                    error: '找不到对应的文件'
                });
                continue;
            }

            try {
                const newFileName = preview.newPath.split('/').pop()!;
                
                // 检查目标文件是否已存在
                if (!allowOverwrite()) {
                    try {
                        await file.parentDir.getFileHandle(newFileName);
                        results.push({
                            success: false,
                            originalPath: preview.originalPath,
                            newPath: preview.newPath,
                            error: `目标文件 "${newFileName}" 已存在`
                        });
                        continue;
                    } catch (e: any) {
                        if (e.name !== 'NotFoundError') {
                            results.push({
                                success: false,
                                originalPath: preview.originalPath,
                                newPath: preview.newPath,
                                error: `检查文件是否存在时出错: ${e.message}`
                            });
                            continue;
                        }
                    }
                }

                // 复制文件内容到新文件
                const originalFile = await file.handle.getFile();
                const content = await originalFile.arrayBuffer();
                
                const newFileHandle = await file.parentDir.getFileHandle(newFileName, { create: true });
                const writable = await newFileHandle.createWritable();
                await writable.write(content);
                await writable.close();

                // 验证新文件
                const newFile = await newFileHandle.getFile();
                if (newFile.size !== originalFile.size) {
                    await file.parentDir.removeEntry(newFileName);
                    results.push({
                        success: false,
                        originalPath: preview.originalPath,
                        newPath: preview.newPath,
                        error: '新文件验证失败：文件大小不匹配'
                    });
                    continue;
                }

                // 删除原文件（如果不是同一个文件）
                const isSameFile = await file.handle.isSameEntry(newFileHandle);
                if (!isSameFile) {
                    await file.parentDir.removeEntry(file.name);
                }

                results.push({
                    success: true,
                    originalPath: preview.originalPath,
                    newPath: preview.newPath
                });

            } catch (error: any) {
                results.push({
                    success: false,
                    originalPath: preview.originalPath,
                    newPath: preview.newPath,
                    error: error.message || '未知错误'
                });
            }
        }

        return results;
    };

    const handleSubmit = async (e: Event) => {
        e.preventDefault();
        
        if (csvData().length === 0) {
            alert('请先上传CSV文件');
            return;
        }
        
        if (files().length === 0) {
            alert('请先选择包含.indd文件的目录');
            return;
        }

        const matchedCount = previewResults().filter(p => p.matched).length;
        if (matchedCount === 0) {
            alert('没有找到匹配的文件进行重命名');
            return;
        }

        if (!confirm(`确定要重命名 ${matchedCount} 个文件吗？`)) {
            return;
        }

        setProcessing(true);
        try {
            const results = await executeRename();
            
            const successCount = results.filter(r => r.success).length;
            const failedCount = results.filter(r => !r.success).length;

            let message = `重命名操作完成！\n成功: ${successCount} 个文件\n`;
            if (failedCount > 0) {
                message += `失败: ${failedCount} 个文件\n\n失败详情:\n`;
                results.filter(r => !r.success).forEach(fail => {
                    message += `- "${fail.originalPath}"\n  原因: ${fail.error}\n`;
                });
            }
            alert(message);

            // 刷新文件列表
            const rootHandle = rootDirectoryHandle();
            if (rootHandle) {
                const updatedFileInfos = await processDirectory(rootHandle, '');
                setFiles(updatedFileInfos);
            }
        } catch (error) {
            alert(`重命名操作失败: ${(error as Error).message}`);
        } finally {
            setProcessing(false);
        }
    };

    // 当CSV数据或文件列表变化时，重新生成预览
    createEffect(() => {
        if (csvData().length > 0 && files().length > 0) {
            generatePreview();
        } else {
            setPreviewResults([]);
        }
    });

    // 当"包含子目录"选项变化时，重新扫描目录
    createEffect(async (prevIncludeSubfolders) => {
        const currentIncludeSubfolders = includeSubfolders();
        const rootHandle = rootDirectoryHandle();

        if (rootHandle && prevIncludeSubfolders !== undefined && prevIncludeSubfolders !== currentIncludeSubfolders) {
            setProcessing(true);
            try {
                const fileInfos = await processDirectory(rootHandle, '');
                setFiles(fileInfos);
                if (fileInfos.length === 0) {
                    alert("更改包含子目录文件选项后，未找到.indd文件。");
                }
            } catch (error) {
                console.error("重新扫描目录时出错:", error);
                alert("重新扫描目录时出错。");
                setFiles([]);
            } finally {
                setProcessing(false);
            }
        }
        return currentIncludeSubfolders;
    });

    return (
        <div class="p-4 bg-gray-100 min-h-screen">
            <div class="max-w-6xl mx-auto bg-white p-6 rounded-lg shadow-md">
                <h1 class="text-3xl font-bold mb-6 text-gray-700">CSV批量重命名INDD文件</h1>
                
                <form onSubmit={handleSubmit} class="space-y-6 mb-8">
                    {/* CSV文件上传区域 */}
                    <div class="p-4 border rounded-md bg-gray-50">
                        <h2 class="text-lg font-semibold mb-3">1. 上传CSV文件</h2>
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">选择包含编号和稿号的CSV文件</span>
                            </label>
                            <input
                                type="file"
                                accept=".csv"
                                class="file-input file-input-bordered w-full"
                                onChange={handleCSVUpload}
                                disabled={processing()}
                            />
                            {csvFile() && (
                                <div class="mt-2">
                                    <div class="text-sm text-green-600 mb-2">
                                        已加载: {csvFile()!.name} ({csvData().length} 条记录)
                                    </div>
                                    {csvData().length > 0 && (
                                        <div class="collapse collapse-arrow bg-base-200">
                                            <input type="checkbox" />
                                            <div class="collapse-title text-sm font-medium">
                                                查看CSV数据预览
                                            </div>
                                            <div class="collapse-content">
                                                <div class="overflow-x-auto max-h-40">
                                                    <table class="table table-xs">
                                                        <thead>
                                                            <tr>
                                                                <th>编号</th>
                                                                <th>稿号</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {csvData().slice(0, 10).map((row, index) => (
                                                                <tr>
                                                                    <td>{row.编号}</td>
                                                                    <td>{row.稿号}</td>
                                                                </tr>
                                                            ))}
                                                            {csvData().length > 10 && (
                                                                <tr>
                                                                    <td colspan="2" class="text-center text-gray-500">
                                                                        ... 还有 {csvData().length - 10} 条记录
                                                                    </td>
                                                                </tr>
                                                            )}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* 目录选择区域 */}
                    <div class="p-4 border rounded-md bg-gray-50">
                        <h2 class="text-lg font-semibold mb-3">2. 选择INDD文件目录</h2>
                        <button
                            type="button"
                            class="btn btn-primary w-full sm:w-auto"
                            onClick={handleDirectorySelect}
                            disabled={processing()}
                        >
                            {processing() && !rootDirectoryHandle() ? '选择中...' : '选择目录'}
                        </button>
                        <div class="mt-3 form-control">
                            <label class="cursor-pointer label justify-start gap-2">
                                <input
                                    type="checkbox"
                                    class="checkbox checkbox-primary"
                                    checked={includeSubfolders()}
                                    onChange={(e) => setIncludeSubfolders(e.currentTarget.checked)}
                                    disabled={processing()}
                                />
                                <span class="label-text">包含子目录文件</span>
                            </label>
                            <label class="cursor-pointer label justify-start gap-2">
                                <input
                                    type="checkbox"
                                    class="checkbox checkbox-accent"
                                    checked={allowOverwrite()}
                                    onChange={(e) => setAllowOverwrite(e.currentTarget.checked)}
                                    disabled={processing()}
                                />
                                <span class="label-text">允许覆盖同名文件</span>
                            </label>
                        </div>
                        {rootDirectoryHandle() && (
                            <div class="mt-2 text-sm text-gray-600">
                                已选择目录: {rootDirectoryHandle()!.name} ({files().length} 个.indd文件)
                            </div>
                        )}
                    </div>

                    {/* 提交按钮 */}
                    <button
                        type="submit"
                        class="btn btn-success w-full sm:w-auto"
                        disabled={csvData().length === 0 || files().length === 0 || processing()}
                    >
                        {processing() ? (
                            <>
                                <span class="loading loading-spinner loading-xs"></span>
                                处理中...
                            </>
                        ) : `确认重命名 ${previewResults().filter(p => p.matched).length} 个文件`}
                    </button>
                </form>

                {/* 预览结果 */}
                {previewResults().length > 0 && !processing() && (
                    <div class="mt-8">
                        <h2 class="text-xl font-semibold mb-4 text-gray-700">
                            预览结果 ({previewResults().filter(p => p.matched).length} 个文件将被重命名，{previewResults().filter(p => !p.matched).length} 个文件未匹配)
                        </h2>

                        {/* 统计信息 */}
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="stat bg-green-50 rounded-lg p-4">
                                <div class="stat-title text-green-700">匹配成功</div>
                                <div class="stat-value text-green-600 text-2xl">{previewResults().filter(p => p.matched).length}</div>
                            </div>
                            <div class="stat bg-yellow-50 rounded-lg p-4">
                                <div class="stat-title text-yellow-700">未匹配</div>
                                <div class="stat-value text-yellow-600 text-2xl">{previewResults().filter(p => !p.matched).length}</div>
                            </div>
                            <div class="stat bg-blue-50 rounded-lg p-4">
                                <div class="stat-title text-blue-700">总文件数</div>
                                <div class="stat-value text-blue-600 text-2xl">{previewResults().length}</div>
                            </div>
                        </div>

                        <div class="overflow-x-auto max-h-96 border rounded-md">
                            <table class="table table-zebra w-full table-sm">
                                <thead class="sticky top-0 bg-gray-200 z-10">
                                    <tr>
                                        <th>状态</th>
                                        <th>原文件路径</th>
                                        <th>新文件路径</th>
                                        <th>编号</th>
                                        <th>稿号</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {previewResults().map(result => (
                                        <tr classList={{
                                            'text-green-600': result.matched,
                                            'text-gray-400': !result.matched
                                        }}>
                                            <td>
                                                {result.matched ? (
                                                    <span class="badge badge-success badge-sm">匹配</span>
                                                ) : (
                                                    <span class="badge badge-warning badge-sm">未匹配</span>
                                                )}
                                            </td>
                                            <td class="break-all text-xs">{result.originalPath}</td>
                                            <td class="break-all text-xs font-medium">{result.newPath}</td>
                                            <td class="text-xs">{result.编号 || '-'}</td>
                                            <td class="text-xs">{result.稿号 || '-'}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}

                {/* 状态提示 */}
                {processing() && (
                    <div class="text-center p-4">
                        <span class="loading loading-lg loading-dots"></span>
                        <p>正在处理，请稍候...</p>
                    </div>
                )}
                
                {csvData().length === 0 && !processing() && (
                    <p class="text-center text-gray-500 mt-8">请先上传CSV文件开始操作</p>
                )}
            </div>
        </div>
    );
}
