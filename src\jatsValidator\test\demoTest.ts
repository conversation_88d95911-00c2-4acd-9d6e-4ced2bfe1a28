// 使用demo.xml文件测试JATS校验器

import { jatsValidator } from '../index';
import { JATSAutoFixer } from '../autoFixer';

// demo.xml的内容（从实际文件中提取的部分）
export const demoXMLContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">
<article article-type="research-article" dtd-version="1.2" xml:lang="en"
    xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <front>
        <journal-meta>
            <journal-id journal-id-type="publisher-id">QIMS</journal-id>
            <journal-id journal-id-type="nlm-ta">Quant Imaging Med Surg</journal-id>
            <journal-title-group>
                <journal-title>Quantitative Imaging in Medicine and Surgery</journal-title>
                <abbrev-journal-title abbrev-type="pubmed">Quant. Imaging Med. Surg.</abbrev-journal-title>
            </journal-title-group>
            <issn pub-type="ppub">2223-4292</issn>
            <issn pub-type="epub">2223-4306</issn>
            <publisher>
                <publisher-name>AME Publishing Company</publisher-name>
            </publisher>
        </journal-meta>
        <article-meta>
            <article-id pub-id-type="publisher-id">qims-24-2439</article-id>
            <article-id pub-id-type="doi">10.21037/qims-24-2439</article-id>
            <article-categories>
                <subj-group subj-group-type="heading">
                    <subject>Original Article</subject>
                </subj-group>
            </article-categories>
            <title-group>
                <article-title>Cervical sliding sign and cervical funneling in the third trimester
                    as predictors of spontaneous preterm birth in singleton pregnancy</article-title>
            </title-group>
            <contrib-group>
                <contrib contrib-type="author">
                    <name>
                        <surname>Zhang</surname>
                        <given-names>Miaomiao</given-names>
                    </name>
                    <xref ref-type="aff" rid="aff1">
                        <sup>1</sup>
                    </xref>
                </contrib>
                <contrib contrib-type="author" corresp="yes">
                    <name>
                        <surname>Yu</surname>
                        <given-names>Hongkui</given-names>
                    </name>
                    <xref ref-type="aff" rid="aff1">
                        <sup>1</sup>
                    </xref>
                </contrib>
            </contrib-group>
            <aff id="aff1">
                <label>1</label>
                <institution content-type="dept">Department of Ultrasonography</institution>, 
                <institution>Shenzhen Baoan Women's and Children's Hospital</institution>, 
                <addr-line>Shenzhen</addr-line>, 
                <country country="cn">China</country>
            </aff>
            <author-notes>
                <fn id="afn1">
                    <p><italic>Contributions:</italic> (I) Conception and design: H Yu, Y Liu, M Zhang</p>
                </fn>
                <corresp id="cor1">
                    <italic>Correspondence to:</italic> Hongkui Yu, PhD. 
                    Email: <email xlink:href="<EMAIL>"><EMAIL></email>.
                </corresp>
            </author-notes>
            <pub-date pub-type="epub">
                <day>30</day>
                <month>06</month>
                <year>2025</year>
            </pub-date>
            <volume>15</volume>
            <issue>7</issue>
            <fpage>6005</fpage>
            <lpage>6015</lpage>
            <history>
                <date date-type="received">
                    <day>04</day>
                    <month>11</month>
                    <year>2024</year>
                </date>
            </history>
            <permissions>
                <copyright-statement>© 2025 AME Publishing Company. All rights reserved.</copyright-statement>
                <copyright-year>2025</copyright-year>
                <copyright-holder>AME Publishing Company.</copyright-holder>
                <license xlink:href="http://creativecommons.org/licenses/by-nc-nd/4.0/">
                    <license-p><italic>Open Access Statement:</italic> This is an Open Access article. 
                    See: <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by-nc-nd/4.0/">https://creativecommons.org/licenses/by-nc-nd/4.0</ext-link>.</license-p>
                </license>
            </permissions>
            <abstract>
                <sec>
                    <title>Background</title>
                    <p>Spontaneous preterm birth (sPTB) is a major cause of neonatal morbidity and mortality.</p>
                </sec>
            </abstract>
            <kwd-group kwd-group-type="author">
                <title>Keywords: </title>
                <kwd>Cervical sliding sign (CSS)</kwd>
                <kwd>funneling</kwd>
            </kwd-group>
            <custom-meta-group>
                <custom-meta>
                    <meta-name>OPEN-ACCESS</meta-name>
                    <meta-value>TRUE</meta-value>
                </custom-meta>
            </custom-meta-group>
        </article-meta>
    </front>
    <body>
        <sec sec-type="intro">
            <title>Introduction</title>
            <p>Spontaneous preterm birth (sPTB) is defined as an unplanned birth before 37 complete weeks of gestation.</p>
        </sec>
    </body>
</article>`;

// 有问题的XML示例（用于测试自动修复）
export const problematicXMLContent = `<?xml version="1.0" encoding="utf-8"?>
<article article-type="article">
<front>
<article-meta>
<article-id pub-id-type="publisher">test-001</article-id>
<title-group>
<article-title>Test Article & Example</article-title>
</title-group>
<contrib-group>
<contrib contrib-type="authors">
<name>
<surname>Test</surname>
<given-names>Author</given-names>
</name>
</contrib>
</contrib-group>
</article-meta>
</front>
<body>
<sec>
<title>Introduction</title>
<p>This is a test paragraph with unescaped & characters and < symbols.</p>
</sec>
</body>
</article>`;

/**
 * 测试demo.xml文件的校验
 */
export function testDemoXML() {
  console.log('🧪 开始测试demo.xml文件...');
  
  const startTime = performance.now();
  const result = jatsValidator.validate(demoXMLContent);
  const endTime = performance.now();
  
  console.log(`📊 校验结果:`);
  console.log(`   状态: ${result.isValid ? '✅ 有效' : '❌ 无效'}`);
  console.log(`   错误: ${result.errors.length} 个`);
  console.log(`   警告: ${result.warnings.length} 个`);
  console.log(`   处理时间: ${(endTime - startTime).toFixed(2)}ms`);
  
  if (result.errors.length > 0) {
    console.log('\n❌ 错误详情:');
    result.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. [${error.code}] ${error.message}`);
      if (error.line) console.log(`      位置: 第${error.line}行`);
    });
  }
  
  if (result.warnings.length > 0) {
    console.log('\n⚠️ 警告详情:');
    result.warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. [${warning.code}] ${warning.message}`);
      if (warning.line) console.log(`      位置: 第${warning.line}行`);
    });
  }
  
  return result;
}

/**
 * 测试自动修复功能
 */
export function testAutoFixer() {
  console.log('\n🔧 开始测试自动修复功能...');
  
  const fixer = new JATSAutoFixer();
  
  console.log('\n📝 原始XML (有问题的):');
  console.log(problematicXMLContent.substring(0, 200) + '...');
  
  const fixResult = fixer.autoFix(problematicXMLContent);
  
  console.log(`\n🔧 修复结果:`);
  console.log(`   是否修复: ${fixResult.fixed ? '✅ 是' : '❌ 否'}`);
  console.log(`   应用的修复: ${fixResult.appliedFixes.length} 个`);
  
  if (fixResult.appliedFixes.length > 0) {
    console.log('\n✨ 应用的修复:');
    fixResult.appliedFixes.forEach((fix, index) => {
      console.log(`   ${index + 1}. ${fix}`);
    });
  }
  
  if (fixResult.remainingErrors.length > 0) {
    console.log(`\n⚠️ 剩余错误: ${fixResult.remainingErrors.length} 个`);
    fixResult.remainingErrors.forEach((error, index) => {
      console.log(`   ${index + 1}. [${error.code}] ${error.message}`);
    });
  } else {
    console.log('\n🎉 所有问题已修复！');
  }
  
  console.log('\n📝 修复后的XML:');
  console.log(fixResult.fixedXML.substring(0, 300) + '...');
  
  return fixResult;
}

/**
 * 运行完整测试套件
 */
export function runDemoTests() {
  console.log('🚀 开始运行demo.xml测试套件...');
  console.log('=' * 50);
  
  // 测试1: 校验demo.xml
  const demoResult = testDemoXML();
  
  // 测试2: 测试自动修复
  const fixResult = testAutoFixer();
  
  // 测试3: 校验修复后的XML
  console.log('\n🔍 校验修复后的XML...');
  const fixedResult = jatsValidator.validate(fixResult.fixedXML);
  console.log(`   修复后状态: ${fixedResult.isValid ? '✅ 有效' : '❌ 无效'}`);
  console.log(`   修复后错误: ${fixedResult.errors.length} 个`);
  console.log(`   修复后警告: ${fixedResult.warnings.length} 个`);
  
  // 总结
  console.log('\n📋 测试总结:');
  console.log(`   demo.xml校验: ${demoResult.isValid ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   自动修复功能: ${fixResult.fixed ? '✅ 工作正常' : '❌ 需要改进'}`);
  console.log(`   修复效果: ${fixedResult.isValid ? '✅ 成功' : '❌ 部分成功'}`);
  
  return {
    demoResult,
    fixResult,
    fixedResult
  };
}

// 导出测试函数供UI使用
export const demoTests = {
  testDemoXML,
  testAutoFixer,
  runDemoTests,
  demoXMLContent,
  problematicXMLContent
};
