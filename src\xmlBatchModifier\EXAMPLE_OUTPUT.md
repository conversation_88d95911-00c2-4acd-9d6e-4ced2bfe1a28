# XML批量修改工具 - 输出示例

## PAP模式输出示例

## Continuous Publication模式输出示例

假设有以下输入文件：
```
input/
├── WK001.xml
├── WK001.pdf
├── WK001_figure1.jpg
├── WK002.xml
├── WK002.pdf
├── WK002_video.mp4
└── cover.jpg
```

### PAP模式输出结构：
```
output/
├── manifest.xml              # 统一的文件清单
└── articles/                # 文章目录
    ├── article-001/          # 文章1目录
    │   ├── article-001.xml   # 重命名后的XML文件
    │   ├── article-001.pdf   # 重命名后的附件
    │   └── article-001_figure1.jpg
    └── article-002/          # 文章2目录
        ├── article-002.xml
        ├── article-002.pdf
        └── article-002_video.mp4
```

### Continuous Publication模式输出结构：
```
output/
├── manifest.xml              # 统一的文件清单
├── issue/                   # 期刊目录
│   ├── issue.xml            # 期刊信息
│   └── cover.jpg            # 封面文件
└── articles/                # 文章目录
    ├── article-001/          # 文章1目录
    │   ├── article-001.xml   # 重命名后的XML文件
    │   ├── article-001.pdf   # 重命名后的附件
    │   └── article-001_figure1.jpg
    └── article-002/          # 文章2目录
        ├── article-002.xml
        ├── article-002.pdf
        └── article-002_video.mp4
```

## manifest.xml 示例内容

```xml
<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <package-meta>
    <package-id>jats-package-1703123456789</package-id>
    <created-date>2023-12-21T10:30:45.123Z</created-date>
    <article-count>2</article-count>
  </package-meta>
  <articles>
    <article>
      <article-id>article-001</article-id>
      <publisher-id>WK001</publisher-id>
      <files>
        <file>articles/article-001/article-001.xml</file>
        <file>articles/article-001/article-001.pdf</file>
        <file>articles/article-001/article-001_figure1.jpg</file>
      </files>
    </article>
    <article>
      <article-id>article-002</article-id>
      <publisher-id>WK002</publisher-id>
      <files>
        <file>articles/article-002/article-002.xml</file>
        <file>articles/article-002/article-002.pdf</file>
        <file>articles/article-002/article-002_video.mp4</file>
      </files>
    </article>
  </articles>
</manifest>
```

## issue.xml 示例内容

```xml
<?xml version="1.0" encoding="UTF-8"?>
<issue>
  <issue-meta>
    <issue-id>issue-1703123456789</issue-id>
    <volume>1</volume>
    <issue>1</issue>
    <pub-date>
      <year>2023</year>
      <month>12</month>
      <day>21</day>
    </pub-date>
  </issue-meta>
  <article-list>
    <article>
      <article-id>article-001</article-id>
      <publisher-id>WK001</publisher-id>
      <title-group>
        <article-title>Article Title</article-title>
      </title-group>
    </article>
    <article>
      <article-id>article-002</article-id>
      <publisher-id>WK002</publisher-id>
      <title-group>
        <article-title>Article Title</article-title>
      </title-group>
    </article>
  </article-list>
</issue>
```

## 平铺模式输出示例

平铺模式下，文件保持在原目录，仅进行重命名：

```
output/
├── article-001.xml           # 重命名后的XML文件
├── article-001.pdf           # 重命名后的附件
├── article-001_figure1.jpg
├── article-002.xml
├── article-002.pdf
└── article-002_video.mp4
```

## 关键特性

1. **双模式支持**: 支持PAP模式和Continuous Publication模式
2. **统一管理**: manifest.xml在根目录创建一份，包含所有文章信息
3. **智能文件处理**: cover文件在Continuous Publication模式下自动移动到issue目录
4. **简化结构**: 所有文章文件放在独立的articles子目录中
5. **完整追踪**: manifest.xml记录每个文章的所有相关文件
6. **标准兼容**: 符合JATS文件包装规范要求

## 处理流程

1. 扫描所有XML文件，提取Publisher ID和新文件名
2. 创建articles目录（结构化模式）
3. 根据发布模式创建issue目录（仅Continuous Publication模式）
4. 处理每个XML文件和相关附件到对应目录
5. 处理cover文件到issue目录（仅Continuous Publication模式）
6. 收集所有文章信息
7. 生成统一的manifest.xml文件
8. 生成issue.xml文件（仅Continuous Publication模式）
