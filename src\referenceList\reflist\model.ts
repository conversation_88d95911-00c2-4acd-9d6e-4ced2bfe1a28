export interface RefList {
    parseXML(xmlStr: string): Document

    getRefList(xmlDoc: Document): RefIdItem[]

    //
    render(): Promise<string>
}


export interface DocSumItem {
    id: string,
    authors: string,
    title: string,
    source: string,
    pubDate: string,
    volume: string,
    issue: string,
    pages: string,
    doi: string,
    pubStatus: string,
    eLocationId: string
}

export interface RefIdItem {
    rId: string,
    pmid?: string
}
