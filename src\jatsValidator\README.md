# JATS XML 校验器

这是一个用于校验JATS XML文档的JavaScript模块，支持格式检查、标签验证、结构检查和属性验证。

## 功能特性

- ✅ **基于demo.xml标准**: 以实际的demo.xml文件为标准，确保校验规则的准确性
- ✅ **自动修复**: 智能修复常见的XML和JATS问题
- ✅ **XML格式化**: 自动格式化XML内容，包括处理缩进和美化格式
- ✅ **XML格式检查**: 检查XML语法正确性，包括标签闭合、字符转义等
- ✅ **JATS合规性检查**: 验证是否符合JATS标准，包括DTD声明、根元素等
- ✅ **结构验证**: 检查元素层次结构是否符合JATS规范
- ✅ **属性验证**: 检查元素属性是否正确使用
- ✅ **标准合规性检查**: 验证XML是否符合demo.xml的标准结构
- ✅ **严格模式**: 可选的严格JATS标准检查
- ✅ **详细错误报告**: 提供具体的错误位置和修复建议

## 快速开始

### 基本使用

```javascript
import { jatsValidator } from './jatsValidator';

// 格式化XML内容
const xmlContent = `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "JATS-journalpublishing1.dtd"><article article-type="research-article"><front><article-meta><title-group><article-title>Example</article-title></title-group></article-meta></front></article>`;

// 格式化XML
const formattedXML = jatsValidator.formatXML(xmlContent);
console.log('格式化后的XML:', formattedXML);

// 校验XML字符串
const result = jatsValidator.validate(xmlContent);

if (result.isValid) {
  console.log('XML文档有效！');
} else {
  console.log(`发现 ${result.errors.length} 个错误`);
  result.errors.forEach(error => {
    console.log(`错误: ${error.message} (第${error.line}行)`);
  });
}
```

### 高级配置

```javascript
import { validateJATSXML } from './jatsValidator';

const options = {
  checkWellFormedness: true,    // 检查XML格式
  checkJATSCompliance: true,    // 检查JATS合规性
  checkStructure: true,         // 检查结构
  checkAttributes: true,        // 检查属性
  strictMode: false,           // 严格模式
  maxErrors: 100              // 最大错误数量
};

const result = validateJATSXML(xmlContent, options);
```

### 创建校验器实例

```javascript
import { JATSValidator } from './jatsValidator';

const validator = new JATSValidator({
  strictMode: true,
  maxErrors: 50
});

const result = validator.validate(xmlContent);
```

## API 参考

### jatsValidator 对象

#### `validate(xmlContent, options?)`
校验XML内容并返回详细结果。

**参数:**
- `xmlContent` (string): 要校验的XML内容
- `options` (ValidatorOptions, 可选): 校验选项

**返回:** `ValidationResult`

#### `isValidJATS(xmlContent)`
快速检查XML是否为有效的JATS格式。

**参数:**
- `xmlContent` (string): 要检查的XML内容

**返回:** `boolean`

#### `getErrorSummary(xmlContent)`
获取XML的错误摘要信息。

**参数:**
- `xmlContent` (string): 要分析的XML内容

**返回:** 错误摘要对象

#### `getDetailedReport(xmlContent, options?)`
获取详细的校验报告。

**参数:**
- `xmlContent` (string): 要校验的XML内容
- `options` (ValidatorOptions, 可选): 校验选项

**返回:** 详细报告对象

#### `formatXML(xmlContent)`
格式化XML内容，包括处理缩进和美化格式。

**参数:**
- `xmlContent` (string): 要格式化的XML内容

**返回:** `string` - 格式化后的XML内容

### 类型定义

#### ValidationResult
```typescript
interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  info: ValidationError[];
  summary: {
    totalErrors: number;
    totalWarnings: number;
    totalInfo: number;
    processingTime: number;
  };
}
```

#### ValidationError
```typescript
interface ValidationError {
  type: 'error' | 'warning' | 'info';
  code: string;
  message: string;
  line?: number;
  column?: number;
  element?: string;
  attribute?: string;
  context?: string;
}
```

#### ValidatorOptions
```typescript
interface ValidatorOptions {
  checkWellFormedness?: boolean;  // 默认: true
  checkJATSCompliance?: boolean;  // 默认: true
  checkStructure?: boolean;       // 默认: true
  checkAttributes?: boolean;      // 默认: true
  strictMode?: boolean;          // 默认: false
  maxErrors?: number;           // 默认: 100
}
```

## 在其他模块中使用

### 在现有的XML处理模块中集成

```javascript
// 在 xmlBatchModifier 中使用
import { jatsValidator } from '../jatsValidator';

function processXmlFile(xmlContent) {
  // 先校验XML
  const validationResult = jatsValidator.validate(xmlContent);
  
  if (!validationResult.isValid) {
    console.warn('XML文档存在问题:', validationResult.errors);
    // 可以选择继续处理或停止
  }
  
  // 继续原有的处理逻辑
  // ...
}
```

### 在参考文献处理中使用

```javascript
// 在 referenceList 中使用
import { jatsValidator } from '../jatsValidator';

function validateXmlBeforeProcessing(xmlDoc) {
  const xmlString = new XMLSerializer().serializeToString(xmlDoc);
  const result = jatsValidator.getErrorSummary(xmlString);
  
  if (result.hasErrors) {
    console.warn(`XML存在 ${result.errorCount} 个错误`);
  }
  
  return result;
}
```

## 错误代码说明

| 错误代码 | 描述 | 严重程度 |
|---------|------|----------|
| `XML_PARSE_ERROR` | XML解析失败 | error |
| `UNCLOSED_TAG` | 标签未闭合 | error |
| `TAG_MISMATCH` | 标签不匹配 | error |
| `UNESCAPED_AMPERSAND` | 未转义的&字符 | error |
| `INVALID_ROOT_ELEMENT` | 无效的根元素 | error |
| `MISSING_REQUIRED_ATTRIBUTE` | 缺少必需属性 | error |
| `MISSING_REQUIRED_CHILD` | 缺少必需子元素 | error |
| `INVALID_ATTRIBUTE_VALUE` | 无效的属性值 | error |
| `DTD_DECLARATION_MISSING` | 缺少DTD声明 | warning |
| `INVALID_JATS_DTD` | 无效的JATS DTD | warning |
| `UNKNOWN_ELEMENT` | 未知元素 | warning |
| `UNKNOWN_ATTRIBUTE` | 未知属性 | warning |

## 支持的JATS元素

校验器支持JATS 1.2标准中的主要元素，包括：

- 文档结构: `article`, `front`, `body`, `back`
- 元数据: `journal-meta`, `article-meta`, `title-group`
- 内容元素: `sec`, `p`, `fig`, `table-wrap`
- 引用: `ref-list`, `ref`, `element-citation`
- 格式化: `bold`, `italic`, `xref`

## 注意事项

1. 校验器基于JATS 1.2标准，但也兼容部分JATS 1.0/1.1特性
2. 严格模式会进行更严格的检查，可能产生更多警告
3. 大型XML文档的校验可能需要一些时间
4. 校验器不会修改原始XML内容，只提供校验结果

## 扩展和自定义

如需添加自定义规则或修改现有规则，请编辑 `rules.ts` 文件中的 `JATS_RULES` 配置。
