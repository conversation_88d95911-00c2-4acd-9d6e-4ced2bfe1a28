import { createSignal, For } from 'solid-js';

interface ProcessResult {
  fileName: string;
  success: boolean;
  originalSize?: number;
  modifiedSize?: number;
  content?: string;
  error?: string;
  type?: 'xml' | 'rename' | 'organize';
  newFileName?: string;
  newPath?: string;
}

interface ExtractedInfo {
  publisherId: string;
  fileName: string;
}

interface FileInfo {
  path: string;
  name: string;
  handle: FileSystemFileHandle;
  parentDir: FileSystemDirectoryHandle;
}

interface OrganizationMode {
  value: 'flat' | 'structured';
  label: string;
  description: string;
}

interface PublicationMode {
  value: 'pap' | 'continuous';
  label: string;
  description: string;
}

interface IssueMetadata {
  month: string;
  year: string;
  volume: string;
  issue: string;
  issueId: string;
}



const XmlBatchModifier = () => {
  const [selectedFiles, setSelectedFiles] = createSignal<FileInfo[]>([]);
  const [articleType, setArticleType] = createSignal('open-access');
  const [organizationMode, setOrganizationMode] = createSignal<'flat' | 'structured'>('flat');
  const [publicationMode, setPublicationMode] = createSignal<'pap' | 'continuous'>('pap');
  const [issueMetadata, setIssueMetadata] = createSignal<IssueMetadata>({
    month: '',
    year: new Date().getFullYear().toString(),
    volume: '',
    issue: '',
    issueId: ''
  });
  const [isProcessing, setIsProcessing] = createSignal(false);
  const [processResults, setProcessResults] = createSignal<ProcessResult[]>([]);
  const [extractedInfo, setExtractedInfo] = createSignal<ExtractedInfo | null>(null);
  const [rootDirectoryHandle, setRootDirectoryHandle] = createSignal<FileSystemDirectoryHandle | null>(null);

  // 进度相关状态
  const [currentProcessingFile, setCurrentProcessingFile] = createSignal<string>('');
  const [processedCount, setProcessedCount] = createSignal(0);
  const [totalCount, setTotalCount] = createSignal(0);
  const [progressPercentage, setProgressPercentage] = createSignal(0);

  // 检查是否可以开始处理
  const canStartProcessing = () => {
    if (isProcessing() || selectedFiles().length === 0) {
      return false;
    }

    // 如果是Continuous Publication模式，检查元数据是否完整
    if (organizationMode() === 'structured' && publicationMode() === 'continuous') {
      const metadata = issueMetadata();
      return metadata.month && metadata.year && metadata.volume && metadata.issue && metadata.issueId;
    }

    return true;
  };

  // 文章类型选项
  const articleTypes = [
    { value: 'open-access', label: 'Open Access' }
  ];

  // 目录整理模式选项
  const organizationModes: OrganizationMode[] = [
    {
      value: 'flat',
      label: '平铺模式 (Flat Structure)',
      description: '所有文件保持在同一目录下，仅进行重命名'
    },
    {
      value: 'structured',
      label: '结构化模式 (Structured Package)',
      description: '按JATS标准创建结构化目录：XML文件、媒体文件、补充材料分别存放'
    }
  ];

  // 发布模式选项
  const publicationModes: PublicationMode[] = [
    {
      value: 'pap',
      label: 'PAP模式',
      description: '不创建issue.xml文件'
    },
    {
      value: 'continuous',
      label: 'Continuous Publication',
      description: '创建issue目录和issue.xml，移动cover文件到issue目录'
    }
  ];

  // 处理目录选择 - 使用File System Access API
  const handleDirectorySelect = async () => {
    try {
      const dirHandle = await window.showDirectoryPicker();
      setRootDirectoryHandle(dirHandle);
      setIsProcessing(true);
      const fileInfos = await processDirectory(dirHandle, '');
      setSelectedFiles(fileInfos);
      if (fileInfos.length === 0) {
        alert("选择的目录中没有找到支持的文件类型。");
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log('目录选择被用户取消');
      } else {
        console.error('选择目录时出错:', error);
        alert(`选择目录失败: ${(error as Error).message || '未知错误'}`);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理目录中的文件
  const processDirectory = async (dirHandle: FileSystemDirectoryHandle, currentPath = ''): Promise<FileInfo[]> => {
    const fileInfos: FileInfo[] = [];
    try {
      for await (const entry of dirHandle.values()) {
        const entryPath = currentPath ? `${currentPath}/${entry.name}` : entry.name;
        if (entry.kind === 'file') {
          const ext = entry.name.toLowerCase();
          // 支持的文件类型
          if (ext.endsWith('.xml') || ext.endsWith('.pdf') ||
            ext.endsWith('.mp4') || ext.endsWith('.jpg') ||
            ext.endsWith('.jpeg') || ext.endsWith('.png') ||
            ext.endsWith('.doc') || ext.endsWith('.docx') ||
            ext.endsWith('.txt') || ext.endsWith('.zip')) {
            fileInfos.push({
              path: entryPath,
              name: entry.name,
              handle: entry as FileSystemFileHandle,
              parentDir: dirHandle
            });
          }
        } else if (entry.kind === 'directory') {
          // 递归处理子目录
          try {
            const subDirHandle = await dirHandle.getDirectoryHandle(entry.name);
            const subFiles = await processDirectory(subDirHandle, entryPath);
            fileInfos.push(...subFiles);
          } catch (subDirError: any) {
            console.warn(`无法访问子目录 "${entryPath}": ${subDirError.message}. 跳过该目录.`);
          }
        }
      }
    } catch (error: any) {
      console.error(`处理目录 "${currentPath || dirHandle.name}" 时出错: ${error.message}`);
    }
    return fileInfos;
  };

  // 从XML中提取信息
  const extractInfoFromXml = (xmlContent: string): ExtractedInfo | null => {
    // 提取 publisher-id
    const publisherIdMatch = xmlContent.match(/<article-id pub-id-type="publisher-id">(.*?)<\/article-id>/);

    // 提取 DOI 中的文件名部分
    const doiMatch = xmlContent.match(/<article-id pub-id-type="doi">10\.21037\/(.*?)<\/article-id>/);

    if (publisherIdMatch && doiMatch) {
      return {
        publisherId: publisherIdMatch[1],
        fileName: doiMatch[1]
      };
    }

    return null;
  };

  // Open Access 类型的XML修改逻辑
  const processOpenAccessXml = (xmlContent: string, info: ExtractedInfo): string => {
    let modifiedContent = xmlContent;


    // 2.1 修改XML声明和DOCTYPE
    modifiedContent = modifiedContent.replace(
      /<\?xml version="1\.0" encoding="utf-8"\?>\s*<!DOCTYPE article PUBLIC "-\/\/NLM\/\/DTD Journal Publishing DTD v3\.0 20080202\/\/EN" "journalpublishing3\.dtd">/g,
      '<?xml version="1.0" encoding="UTF-8" standalone="no"?><!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN" "http://jats.nlm.nih.gov/publishing/1.2/JATS-journalpublishing1.dtd">'
    );

    // 2.2 修改article标签属性
    modifiedContent = modifiedContent.replace(
      /dtd-version="3\.0" xml:lang="en" xmlns:xlink="http:\/\/www\.w3\.org\/1999\/xlink" xmlns:mml="http:\/\/www\.w3\.org\/1998\/Math\/MathML"/g,
      'dtd-version="1.2" xml:lang="en" xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"'
    );

    // 2.3 在permissions标签中插入license信息
    const licenseContent = `<license xlink:href="http://creativecommons.org/licenses/by-nc-nd/4.0/"><license-p><italic>Open Access Statement:</italic> This is an Open Access article distributed in accordance with the Creative Commons Attribution-NonCommercial-NoDerivs 4.0 International License (CC BY-NC-ND 4.0), which permits the non-commercial replication and distribution of the article with the strict proviso that no changes or edits are made and the original work is properly cited (including links to both the formal publication through the relevant DOI and the license). See: <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by-nc-nd/4.0/">https://creativecommons.org/licenses/by-nc-nd/4.0</ext-link>.</license-p></license>`;
    // 2.3 在permissions标签末尾插入license信息
    modifiedContent = modifiedContent.replace(
      /<\/permissions>/g,
      `${licenseContent}</permissions>`
    );


    // // 2.3 删除journal-id标签
    // modifiedContent = modifiedContent.replace(
    //   /<journal-id journal-id-type="nlm-ta">.*?<\/journal-id>\s*/g,
    //   ''
    // );

    // // 2.4 修改abbrev-journal-title标签
    // modifiedContent = modifiedContent.replace(
    //   /<abbrev-journal-title abbrev-type="pubmed">(.*?)<\/abbrev-journal-title>/g,
    //   '<abbrev-journal-title>$1</abbrev-journal-title>'
    // );

    // // 2.5 删除equal-contrib属性
    // modifiedContent = modifiedContent.replace(
    //   /(<contrib contrib-type="author") equal-contrib="yes">/g,
    //   '$1>'
    // );

    // // 2.6 替换history标签为fn标签
    // modifiedContent = modifiedContent.replace(
    //   /<history>[\s\S]*?<\/history>/g,
    //   (match: string) => {
    //     // 尝试提取原始日期信息
    //     const receivedMatch = match.match(/<date date-type="received">[\s\S]*?<day>(\d+)<\/day>[\s\S]*?<month>(\d+)<\/month>[\s\S]*?<year>(\d+)<\/year>[\s\S]*?<\/date>/);
    //     const acceptedMatch = match.match(/<date date-type="accepted">[\s\S]*?<day>(\d+)<\/day>[\s\S]*?<month>(\d+)<\/month>[\s\S]*?<year>(\d+)<\/year>[\s\S]*?<\/date>/);

    //     let submittedDate = 'May 30, 2024';
    //     let acceptedDate = 'October 11, 2024';

    //     if (receivedMatch) {
    //       const months = ['January', 'February', 'March', 'April', 'May', 'June', 
    //                      'July', 'August', 'September', 'October', 'November', 'December'];
    //       submittedDate = `${months[parseInt(receivedMatch[2]) - 1]} ${parseInt(receivedMatch[1])}, ${receivedMatch[3]}`;
    //     }

    //     if (acceptedMatch) {
    //       const months = ['January', 'February', 'March', 'April', 'May', 'June', 
    //                      'July', 'August', 'September', 'October', 'November', 'December'];
    //       acceptedDate = `${months[parseInt(acceptedMatch[2]) - 1]} ${parseInt(acceptedMatch[1])}, ${acceptedMatch[3]}`;
    //     }

    //     return `<fn fn-type="other"><p><bold>Submitted:</bold> ${submittedDate}</p><p><bold>Accepted:</bold> ${acceptedDate}</p></fn>`;
    //   }
    // );

    // // 2.7 摘要小标题改为大写 - 只在abstract标签内处理
    // const abstractMatch = modifiedContent.match(/<abstract[\s\S]*?<\/abstract>/);
    // if (abstractMatch) {
    //   const abstractContent = abstractMatch[0];
    //   const modifiedAbstract = abstractContent.replace(
    //     /<title>([^<]+)<\/title>/g,
    //     (_match: string, title: string) => `<title>${title.toUpperCase()}</title>`
    //   );
    //   modifiedContent = modifiedContent.replace(abstractContent, modifiedAbstract);
    // }

    // 2.8 在</article-meta>前添加custom-meta-group
    modifiedContent = modifiedContent.replace(
      /<\/article-meta>/g,
      '<custom-meta-group><custom-meta><meta-name>OPEN-ACCESS</meta-name><meta-value>TRUE</meta-value></custom-meta></custom-meta-group></article-meta>'
    );

    // // 2.8 修改引用文献的rid属性（r改为bib）
    // modifiedContent = modifiedContent.replace(
    //   /rid="r(\d+)"/g,
    //   'rid="bib$1"'
    // );

    // // 修改文献列表的id属性
    // modifiedContent = modifiedContent.replace(
    //   /<ref id="r(\d+)">/g,
    //   '<ref id="bib$1">'
    // );

    // // 2.9 删除文献列表中的pmid和doi链接
    // modifiedContent = modifiedContent.replace(
    //   /<pub-id pub-id-type="pmid">.*?<\/pub-id>\s*/g,
    //   ''
    // );
    // modifiedContent = modifiedContent.replace(
    //   /<pub-id pub-id-type="doi">.*?<\/pub-id>\s*/g,
    //   ''
    // );

    // 使用提取的信息进行替换
    // 替换publisher-id中的内容为extractedFileName
    modifiedContent = modifiedContent.replace(
      /<article-id pub-id-type="publisher-id">.*?<\/article-id>/g,
      `<article-id pub-id-type="publisher-id">${info.fileName}</article-id>`
    );

    // 批量替换内容中的publisher-id为新的文件名
    const regex = new RegExp(info.publisherId, 'g');
    modifiedContent = modifiedContent.replace(regex, info.fileName);

    // footnote 加标题
    modifiedContent = modifiedContent.replace(
      /<fn-group>/g,
      '<sec sec-type="Footnote"><title>Footnote</title><fn-group>'
    );

    modifiedContent = modifiedContent.replace(
      /<\/fn-group>/g,
      '</fn-group></sec>'
    );

    // 视频media 移动到正文下方，添加标签
    const mediaMatches = modifiedContent.match(/<media[^>]*>[\s\S]*?(<\/media>)?/g);
    console.log(mediaMatches)
    if (mediaMatches) {
      // 先删除原有的media标签
      mediaMatches.forEach(media => {
        modifiedContent = modifiedContent.replace(media, '');
      });

      // 转换 media 标签
      let counter = 1;
      const transformedMedia = mediaMatches.map(media => {
        // 提取 href
        const hrefMatch = media.match(/xlink:href="([^"]+)"/);
        let href = hrefMatch ? hrefMatch[1] : '';

        // 补充 .mp4 后缀
        if (href && !href.endsWith('.mp4')) {
          href = href.replace(/\.*$/, '') + '.mp4';
        }

        return `<supplementary-material id="s${counter++}" mimetype="video" xlink:href="${href}" mime-subtype="mp4"/>`;
      });


      // 在</body>和<back>之间添加media标签，包装在sec和supplementary-material中
      // 在</body>前插入
      const mediaSection = `<sec sec-type="supplementary-material">
      ${transformedMedia.join('\n')}
      </sec>`;
      modifiedContent = modifiedContent.replace(
        /<\/body>/,
        `</body>\n${mediaSection}`
      );
    }

    // related-article 删除多余属性，调整顺序
    modifiedContent = modifiedContent.replace(
      /<related-article[^>]*>/g,
      (match) => {
        // 提取必要的属性
        const typeMatch = match.match(/related-article-type="([^"]*)"/);
        const idMatch = match.match(/id="([^"]*)"/);
        const xlinkMatch = match.match(/xlink:href="([^"]*)"/);

        let newTag = '<related-article';
        if (idMatch) newTag += ` id="${idMatch[1]}"`;
        newTag += ` ext-link-type="doi"`
        if (typeMatch) newTag += ` related-article-type="${typeMatch[1]}"`;
        if (xlinkMatch) newTag += ` xlink:href="${xlinkMatch[1]}"`;
        newTag += '>';

        return newTag;
      }
    );
    // table-wrap-foot 标签处理
    modifiedContent = modifiedContent.replace(
      /<table-wrap-foot>([\s\S]*?)<\/table-wrap-foot>/g,
      (_match, content) => {
        // 为每个p标签外面包装fn标签
        const wrappedContent = content.replace(
          /<p>([\s\S]*?)<\/p>/g,
          '<fn><p>$1</p></fn>'
        );
        return `<table-wrap-foot>${wrappedContent}</table-wrap-foot>`;
      }
    );

     // 2.9 修改video相关的xref标签
     modifiedContent = modifiedContent.replace(
      /<xref ref-type="other" rid="vid/g,
      '<xref ref-type="supplementary-material" rid="s'
    );

    // 2.10 删除视频标签 (fig标签中包含fig-type="video"的)
    modifiedContent = modifiedContent.replace(
      /<fig[^>]*fig-type="video"[^>]*>[\s\S]*?<\/fig>/g,
      ''
    );

    // 移动 footnote 标签到 </body>前面
    const footnoteMatch = modifiedContent.match(/<sec sec-type="Footnote"><title>Footnote<\/title>[\s\S]*?<\/sec>/);
     if (footnoteMatch) {
       const footnoteSection = footnoteMatch[0];
       // 删除原位置的 Acknowledgments
       modifiedContent = modifiedContent.replace(footnoteSection, '');
       
       // 在 Footnote 前面插入 Acknowledgments
       modifiedContent = modifiedContent.replace(
         /<\/body>/,
         `${footnoteSection}\n<\/body>`
       );
     }


    //  修改 Acknowledgments
    modifiedContent = modifiedContent.replace(
      /<ack>([\s\S]*?)<\/ack>/g,
      '<sec sec-type="Acknowledgments">$1</sec>'
    );


     // 移动 Acknowledgments 标签到 Footnote 前面
     const acknowledgmentsMatch = modifiedContent.match(/<sec sec-type="Acknowledgments">[\s\S]*?<\/sec>/);
     if (acknowledgmentsMatch) {
       const acknowledgmentsSection = acknowledgmentsMatch[0];
       // 删除原位置的 Acknowledgments
       modifiedContent = modifiedContent.replace(acknowledgmentsSection, '');
       
       // 在 Footnote 前面插入 Acknowledgments
       modifiedContent = modifiedContent.replace(
         /<sec sec-type="Footnote"><title>Footnote<\/title>/,
         `${acknowledgmentsSection}\n<sec sec-type="Footnote"><title>Footnote</title>`
       );
     }

    // 清理多余的空行和空白
    modifiedContent = modifiedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

    return modifiedContent;
  };

  // 创建目录结构（结构化模式）
  const createDirectoryStructure = async (rootDir: FileSystemDirectoryHandle, articleId: string) => {
    const articlesDir = await rootDir.getDirectoryHandle('articles', { create: true });
    const articleDir = await articlesDir.getDirectoryHandle(articleId, { create: true });

    let issueDir;
    if (publicationMode() === 'continuous') {
      issueDir = await rootDir.getDirectoryHandle('issue', { create: true });
    }

    return {
      root: rootDir,
      articles: articlesDir,
      articleDir: articleDir,
      issueDir: issueDir
    };
  };

  // 创建manifest.xml文件（根目录，根据发布模式生成不同格式）
  const createManifestXml = async (
    rootDir: FileSystemDirectoryHandle,
    allArticlesInfo: Array<{info: ExtractedInfo, files: string[]}>
  ) => {
    let manifestContent: string;

    if (publicationMode() === 'continuous') {
      // Continuous Publication模式：包含issue部分和空的articles部分
      const metadata = issueMetadata();

      // 获取issue目录中的文件列表
      const issueFiles: string[] = [];
      try {
        const issueDir = await rootDir.getDirectoryHandle('issue');

        // 添加issue XML文件
        issueFiles.push(`${metadata.issueId}.xml`);

        // 检查是否有cover文件
        for await (const [name, handle] of issueDir.entries()) {
          if (handle.kind === 'file' && name.toLowerCase().includes('cover')) {
            issueFiles.push(name);
          }
        }
      } catch (error) {
        console.error('读取issue目录失败:', error);
      }

      manifestContent = `<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE submission PUBLIC "-//WK//WK-Manifest.dtd DTD v1.0 201801003//EN" "../../manifest-4.1/submissionmanifest.4.1.dtd">
<submission>
    <product-id>ESUTECH</product-id>
    <issue>
        <issue-id>${metadata.issueId}</issue-id>
        <files>
            ${issueFiles.map(file => `<file>${file}</file>`).join('\n            ')}
        </files>
    </issue>
    <articles></articles>
</submission>`;
    } else {
      // PAP模式：原有格式
      manifestContent = `<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <package-meta>
    <package-id>jats-package-${new Date().getTime()}</package-id>
    <created-date>${new Date().toISOString()}</created-date>
    <article-count>${allArticlesInfo.length}</article-count>
  </package-meta>
  <articles>
    ${allArticlesInfo.map(article => `
    <article>
      <article-id>${article.info.fileName}</article-id>
      <publisher-id>${article.info.publisherId}</publisher-id>
      <files>
        ${article.files.map(fileName => `<file>articles/${article.info.fileName}/${fileName}</file>`).join('\n        ')}
      </files>
    </article>`).join('')}
  </articles>
</manifest>`;
    }

    try {
      const manifestHandle = await rootDir.getFileHandle('manifest.xml', { create: true });
      const writable = await manifestHandle.createWritable();
      await writable.write(manifestContent);
      await writable.close();
      return true;
    } catch (error) {
      console.error('创建manifest.xml失败:', error);
      return false;
    }
  };

  // 从XML文件中提取文章信息
  const extractArticleInfo = async (xmlContent: string) => {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(xmlContent, 'text/xml');

      // 提取DOI
      const doiElement = doc.querySelector('article-id[pub-id-type="doi"]');
      const doi = doiElement?.textContent?.trim() || '';

      // 提取文章类型
      const subjectElement = doc.querySelector('article-categories subj-group[subj-group-type="heading"] subject');
      const articleType = subjectElement?.textContent?.trim() || 'Other';

      return { doi, articleType };
    } catch (error) {
      console.error('解析XML文件失败:', error);
      return { doi: '', articleType: 'Other' };
    }
  };

  // 创建issue.xml文件（仅在continuous模式下）
  const createIssueXml = async (
    issueDir: FileSystemDirectoryHandle,
    allArticlesInfo: Array<{info: ExtractedInfo, files: string[]}>
  ) => {
    const metadata = issueMetadata();

    // 从articles目录读取XML文件并提取信息
    const articlesWithTypes: Array<{doi: string, type: string}> = [];

    try {
      const rootDir = rootDirectoryHandle()!;
      const articlesDir = await rootDir.getDirectoryHandle('articles');

      for (const articleInfo of allArticlesInfo) {
        try {
          const articleDir = await articlesDir.getDirectoryHandle(articleInfo.info.fileName);
          const xmlFileName = `${articleInfo.info.fileName}.xml`;
          const xmlHandle = await articleDir.getFileHandle(xmlFileName);
          const xmlFile = await xmlHandle.getFile();
          const xmlContent = await xmlFile.text();

          const { doi, articleType } = await extractArticleInfo(xmlContent);
          if (doi) {
            articlesWithTypes.push({ doi, type: articleType });
          }
        } catch (error) {
          console.error(`读取文章 ${articleInfo.info.fileName} 失败:`, error);
        }
      }
    } catch (error) {
      console.error('读取articles目录失败:', error);
    }

    // 按文章类型分组
    const groupedArticles: Record<string, string[]> = {};
    articlesWithTypes.forEach(article => {
      if (!groupedArticles[article.type]) {
        groupedArticles[article.type] = [];
      }
      groupedArticles[article.type].push(article.doi);
    });

    // 生成TOC结构
    const tocGroups = Object.entries(groupedArticles).map(([type, dois]) => `
        <issue-subject-group>
            <issue-subject-title>
                <subject>${type}</subject>
            </issue-subject-title>
            ${dois.map(doi => `
            <issue-article-meta>
                <article-id pub-id-type="doi">${doi}</article-id>
            </issue-article-meta>`).join('')}
        </issue-subject-group>`).join('');

    const issueContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE issue-xml PUBLIC "-//WK//DTD WK JATS Journal Archiving and Interchange Issue XML DTD v1.0 20181003//EN" "WK-Issue.dtd">
<issue-xml submission-type="partial">
    <journal-meta>
        <journal-id journal-id-type="hwp">jbjsest</journal-id>
        <journal-id journal-id-type="publisher-id">jbjsest</journal-id>
        <journal-title-group>
            <journal-title>JBJS Essential Surgical Techniques</journal-title>
            <abbrev-journal-title abbrev-type="full">JBJS Essential Surgical Techniques</abbrev-journal-title>
        </journal-title-group>
        <issn pub-type="epub">2160-2204</issn>
        <publisher>
            <publisher-name>The Journal of Bone and Joint Surgery, Inc.</publisher-name>
        </publisher>
    </journal-meta>
    <issue-meta>
        <pub-date pub-type="collection">
            <month>${metadata.month}</month>
            <year>${metadata.year}</year>
        </pub-date>
        <volume>${metadata.volume}</volume>
        <issue>${metadata.issue}</issue>
        <issue-id>${metadata.issueId}</issue-id>
    </issue-meta>
    <toc toc-type="subject-heading-order">${tocGroups}
    </toc>
</issue-xml>`;

    try {
      const issueFileName = `${metadata.issueId}.xml`;
      const issueHandle = await issueDir.getFileHandle(issueFileName, { create: true });
      const writable = await issueHandle.createWritable();
      await writable.write(issueContent);
      await writable.close();
      return true;
    } catch (error) {
      console.error('创建issue.xml失败:', error);
      return false;
    }
  };

  // 获取文件类型分类
  const getFileCategory = (fileName: string): 'xml' | 'attachment' | 'cover' => {
    const ext = fileName.toLowerCase();
    if (ext.endsWith('.xml')) return 'xml';
    if (fileName.toLowerCase().includes('cover')) return 'cover';
    return 'attachment'; // 所有其他附件都放在articles目录
  };

  // 处理cover文件移动到issue目录
  const processCoverFile = async (
    fileInfo: FileInfo,
    issueDir: FileSystemDirectoryHandle
  ): Promise<ProcessResult> => {
    try {
      const file = await fileInfo.handle.getFile();
      const newFileHandle = await issueDir.getFileHandle(fileInfo.name, { create: true });
      const writable = await newFileHandle.createWritable();
      await writable.write(file);
      await writable.close();

      return {
        fileName: fileInfo.name,
        success: true,
        newFileName: fileInfo.name,
        newPath: `issue/${fileInfo.name}`,
        type: 'organize'
      };
    } catch (error) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `移动cover文件失败: ${error}`,
        type: 'organize'
      };
    }
  };

  // 实际重命名/移动文件
  const renameOrMoveFile = async (
    fileInfo: FileInfo,
    newFileName: string,
    targetDir?: FileSystemDirectoryHandle
  ): Promise<ProcessResult> => {
    const targetDirectory = targetDir || fileInfo.parentDir;
    const isMove = targetDir && targetDir !== fileInfo.parentDir;

    if (fileInfo.name === newFileName && !isMove) {
      return {
        fileName: fileInfo.name,
        success: true,
        newFileName: newFileName,
        type: isMove ? 'organize' : 'rename'
      };
    }

    try {
      // 检查目标文件是否已存在
      try {
        await targetDirectory.getFileHandle(newFileName);
        return {
          fileName: fileInfo.name,
          success: false,
          error: `目标文件 "${newFileName}" 已存在`,
          type: isMove ? 'organize' : 'rename'
        };
      } catch (e: any) {
        if (e.name !== 'NotFoundError') {
          throw e;
        }
      }

      // 读取原文件内容
      const originalFile = await fileInfo.handle.getFile();
      const content = await originalFile.arrayBuffer();

      // 创建新文件
      const newFileHandle = await targetDirectory.getFileHandle(newFileName, { create: true });
      const writable = await newFileHandle.createWritable();
      await writable.write(content);
      await writable.close();

      // 验证新文件
      const newFile = await newFileHandle.getFile();
      if (newFile.size !== originalFile.size) {
        await targetDirectory.removeEntry(newFileName);
        return {
          fileName: fileInfo.name,
          success: false,
          error: `新文件验证失败：文件大小不匹配`,
          type: isMove ? 'organize' : 'rename'
        };
      }

      // 检查是否是同一个文件（某些文件系统可能会这样）
      const isSameFile = await fileInfo.handle.isSameEntry(newFileHandle);
      if (!isSameFile) {
        // 删除原文件
        await fileInfo.parentDir.removeEntry(fileInfo.name);
      }

      return {
        fileName: fileInfo.name,
        success: true,
        newFileName: newFileName,
        newPath: isMove ? `${targetDirectory.name}/${newFileName}` : undefined,
        type: isMove ? 'organize' : 'rename'
      };
    } catch (error: any) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `${isMove ? '移动' : '重命名'}失败: ${error.message}`,
        type: isMove ? 'organize' : 'rename'
      };
    }
  };

  // 处理文件重命名或组织
  const processFileOrganization = async (
    fileInfo: FileInfo,
    info: ExtractedInfo,
    directories?: { root: FileSystemDirectoryHandle; articles: FileSystemDirectoryHandle; articleDir: FileSystemDirectoryHandle; issueDir?: FileSystemDirectoryHandle }
  ): Promise<ProcessResult> => {
    if (!fileInfo.name.includes(info.publisherId)) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `文件名中未找到 "${info.publisherId}"`,
        type: organizationMode() === 'structured' ? 'organize' : 'rename'
      };
    }

    const newFileName = fileInfo.name.replace(new RegExp(info.publisherId, 'g'), info.fileName);

    if (organizationMode() === 'flat') {
      // 平铺模式：仅重命名
      return await renameOrMoveFile(fileInfo, newFileName);
    } else {
      // 结构化模式：重命名并移动到相应目录
      if (!directories) {
        return {
          fileName: fileInfo.name,
          success: false,
          error: '结构化模式下缺少目录信息',
          type: 'organize'
        };
      }

      const category = getFileCategory(fileInfo.name);

      // 处理cover文件（仅在continuous模式下移动到issue目录）
      if (category === 'cover' && publicationMode() === 'continuous' && directories.issueDir) {
        return await processCoverFile(fileInfo, directories.issueDir);
      }

      // 其他文件（XML和附件）都放在文章专属目录下
      return await renameOrMoveFile(fileInfo, newFileName, directories.articleDir);
    }
  };

  // 处理单个XML文件
  // 保存XML文件
  const saveXmlFile = async (
    fileInfo: FileInfo,
    content: string,
    newFileName: string,
    originalSize: number,
    targetDir?: FileSystemDirectoryHandle
  ): Promise<ProcessResult> => {
    const targetDirectory = targetDir || fileInfo.parentDir;
    const isMove = targetDir && targetDir !== fileInfo.parentDir;

    try {
      // 如果文件名没有变化且不需要移动，直接覆盖原文件
      if (fileInfo.name === newFileName && !isMove) {
        const writable = await fileInfo.handle.createWritable();
        await writable.write(content);
        await writable.close();

        return {
          fileName: fileInfo.name,
          success: true,
          originalSize: originalSize,
          modifiedSize: content.length,
          content: content,
          newFileName: newFileName,
          type: 'xml'
        };
      } else {
        // 需要重命名或移动，先保存新文件再删除旧文件
        try {
          await targetDirectory.getFileHandle(newFileName);
          return {
            fileName: fileInfo.name,
            success: false,
            error: `目标文件 "${newFileName}" 已存在`,
            type: 'xml'
          };
        } catch (e: any) {
          if (e.name !== 'NotFoundError') {
            throw e;
          }
        }

        // 创建新文件
        const newFileHandle = await targetDirectory.getFileHandle(newFileName, { create: true });
        const writable = await newFileHandle.createWritable();
        await writable.write(content);
        await writable.close();

        // 验证新文件
        const newFile = await newFileHandle.getFile();
        if (newFile.size !== content.length) {
          await targetDirectory.removeEntry(newFileName);
          return {
            fileName: fileInfo.name,
            success: false,
            error: `新文件验证失败：文件大小不匹配`,
            type: 'xml'
          };
        }

        // 检查是否是同一个文件
        const isSameFile = await fileInfo.handle.isSameEntry(newFileHandle);
        if (!isSameFile) {
          // 删除原文件
          await fileInfo.parentDir.removeEntry(fileInfo.name);
        }

        return {
          fileName: fileInfo.name,
          success: true,
          originalSize: originalSize,
          modifiedSize: content.length,
          content: content,
          newFileName: newFileName,
          newPath: isMove ? `${targetDirectory.name}/${newFileName}` : undefined,
          type: 'xml'
        };
      }
    } catch (error: any) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: `保存XML文件失败: ${error.message}`,
        type: 'xml'
      };
    }
  };

  const processXmlFile = async (
    fileInfo: FileInfo,
    info: ExtractedInfo,
    targetDir?: FileSystemDirectoryHandle
  ): Promise<ProcessResult> => {
    try {
      const file = await fileInfo.handle.getFile();
      const originalContent = await file.text();
      let modifiedContent = originalContent;

      if (articleType() === 'open-access') {
        modifiedContent = processOpenAccessXml(originalContent, info);
      }

      // 生成新的XML文件名
      const newXmlFileName = fileInfo.name.includes(info.publisherId)
        ? fileInfo.name.replace(new RegExp(info.publisherId, 'g'), info.fileName)
        : `${info.fileName}.xml`;

      // 保存文件
      return await saveXmlFile(fileInfo, modifiedContent, newXmlFileName, file.size, targetDir);
    } catch (error) {
      return {
        fileName: fileInfo.name,
        success: false,
        error: error instanceof Error ? error.message : '处理文件时发生错误',
        type: 'xml'
      };
    }
  };

  // 批量处理文件
  const handleBatchProcess = async () => {
    if (selectedFiles().length === 0) {
      alert('请先选择文件');
      return;
    }

    // 验证Continuous Publication模式下的必需字段
    if (organizationMode() === 'structured' && publicationMode() === 'continuous') {
      const metadata = issueMetadata();
      if (!metadata.month || !metadata.year || !metadata.volume || !metadata.issue || !metadata.issueId) {
        alert('在Continuous Publication模式下，请填写所有期刊元数据字段（月份、年份、卷号、期号、Issue ID）');
        return;
      }
    }

    setIsProcessing(true);
    setProcessResults([]);

    // 找到所有XML文件
    const xmlFiles = selectedFiles().filter(f => f.name.toLowerCase().endsWith('.xml'));
    const otherFiles = selectedFiles().filter(f => !f.name.toLowerCase().endsWith('.xml'));

    if (xmlFiles.length === 0) {
      alert('请至少选择一个XML文件以提取Publisher ID和文件名信息');
      setIsProcessing(false);
      return;
    }

    try {
      const results: ProcessResult[] = [];
      const processedFiles = new Set<string>(); // 跟踪已处理的文件
      const allArticlesInfo: Array<{info: ExtractedInfo, files: string[]}> = []; // 收集所有文章信息

      // 计算总文件数
      const totalFiles = selectedFiles().length;
      setTotalCount(totalFiles);
      setProcessedCount(0);
      setProgressPercentage(0);

      let currentIndex = 0;

      // 处理每个XML文件及其相关附件
      for (const xmlFile of xmlFiles) {
        setCurrentProcessingFile(`正在处理XML文件: ${xmlFile.name}`);

        // 从当前XML文件提取信息
        const file = await xmlFile.handle.getFile();
        const xmlContent = await file.text();
        const info = extractInfoFromXml(xmlContent);

        if (!info) {
          results.push({
            fileName: xmlFile.name,
            success: false,
            error: '无法从XML文件中提取Publisher ID和DOI信息',
            type: 'xml'
          });
          currentIndex++;
          setProcessedCount(currentIndex);
          setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));
          continue;
        }

        // 设置提取的信息（用于UI显示）
        if (!extractedInfo()) {
          setExtractedInfo(info);
        }

        // 当前文章的文件列表
        const articleFiles: string[] = [];

        // 创建当前文章的目录结构（如果是结构化模式）
        let directories;
        if (organizationMode() === 'structured' && rootDirectoryHandle()) {
          directories = await createDirectoryStructure(rootDirectoryHandle()!, info.fileName);
        }

        // 处理当前XML文件
        const xmlTargetDir = organizationMode() === 'structured' && directories ? directories.articleDir : undefined;
        const xmlResult = await processXmlFile(xmlFile, info, xmlTargetDir);
        results.push(xmlResult);
        processedFiles.add(xmlFile.path);

        if (xmlResult.success && xmlResult.newFileName) {
          articleFiles.push(xmlResult.newFileName);
        }

        currentIndex++;
        setProcessedCount(currentIndex);
        setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));

        // 查找与当前XML相关的附件文件（包含相同的Publisher ID）
        const regexName = new RegExp(`${info.publisherId}\.(\\w+)`)
        const relatedFiles = otherFiles.filter(otherFile =>
          !processedFiles.has(otherFile.path) &&
          regexName.test(otherFile.path)
        );

        // 处理相关附件文件
        for (const relatedFile of relatedFiles) {
          const actionText = organizationMode() === 'structured' ? '正在整理附件' : '正在重命名附件';
          setCurrentProcessingFile(`${actionText}: ${relatedFile.name}`);
          const organizeResult = await processFileOrganization(relatedFile, info, directories);
          results.push(organizeResult);
          processedFiles.add(relatedFile.path);

          if (organizeResult.success && organizeResult.newFileName) {
            articleFiles.push(organizeResult.newFileName);
          }

          currentIndex++;
          setProcessedCount(currentIndex);
          setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));
        }

        // 将当前文章信息添加到总列表
        if (info) {
          allArticlesInfo.push({
            info: info,
            files: articleFiles
          });
        }
      }

      // 创建manifest.xml和issue.xml（仅在结构化模式下，在所有文章处理完后）
      if (organizationMode() === 'structured' && rootDirectoryHandle() && allArticlesInfo.length > 0) {
        setCurrentProcessingFile('正在创建manifest.xml...');

        const manifestCreated = await createManifestXml(rootDirectoryHandle()!, allArticlesInfo);

        if (manifestCreated) {
          results.push({
            fileName: 'manifest.xml',
            success: true,
            newFileName: 'manifest.xml',
            newPath: 'manifest.xml',
            type: 'organize'
          });
        }

        // 只在continuous模式下创建issue.xml
        if (publicationMode() === 'continuous') {
          setCurrentProcessingFile('正在创建issue.xml...');

          // 创建issue目录（如果还没有创建）
          const issueDir = await rootDirectoryHandle()!.getDirectoryHandle('issue', { create: true });
          const issueCreated = await createIssueXml(issueDir, allArticlesInfo);

          if (issueCreated) {
            results.push({
              fileName: 'issue.xml',
              success: true,
              newFileName: 'issue.xml',
              newPath: 'issue/issue.xml',
              type: 'organize'
            });
          }
        }
      }

      // 处理剩余的未匹配文件（显示为跳过）
      const remainingFiles = otherFiles.filter(f => !processedFiles.has(f.path));
      for (const remainingFile of remainingFiles) {
        setCurrentProcessingFile(`跳过未匹配文件: ${remainingFile.name}`);
        results.push({
          fileName: remainingFile.name,
          success: false,
          error: '未找到匹配的XML文件或Publisher ID',
          type: 'rename'
        });
        currentIndex++;
        setProcessedCount(currentIndex);
        setProgressPercentage(Math.round((currentIndex / totalFiles) * 100));
      }

      setProcessResults(results);
      setCurrentProcessingFile('处理完成');

      // 显示处理结果
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;
      const xmlCount = results.filter(r => r.type === 'xml' && r.success).length;
      const renameCount = results.filter(r => r.type === 'rename' && r.success).length;
      const organizeCount = results.filter(r => r.type === 'organize' && r.success).length;

      let message = `处理完成！\n`;
      message += `成功处理: ${successCount} 个文件\n`;
      message += `  - XML文件: ${xmlCount} 个\n`;
      if (organizationMode() === 'structured') {
        message += `  - 整理文件: ${organizeCount} 个\n`;
      } else {
        message += `  - 重命名文件: ${renameCount} 个\n`;
      }
      if (failedCount > 0) {
        message += `失败: ${failedCount} 个文件\n`;
      }
      alert(message);

      // 重置进度状态
      setTimeout(() => {
        setCurrentProcessingFile('');
        setProcessedCount(0);
        setTotalCount(0);
        setProgressPercentage(0);
      }, 2000);

      setIsProcessing(false);
    } catch (error) {
      alert('处理文件时发生错误: ' + (error instanceof Error ? error.message : '未知错误'));
      setIsProcessing(false);
    }
  };



  return (
    <div class="p-6 max-w-6xl mx-auto">
      <div class="mb-6">
        <h1 class="text-3xl font-bold mb-2">批量XML文件修改工具（威科规范）</h1>
        <p class="text-gray-600 mb-4">自动从XML文件中提取Publisher ID和文件名信息，支持两种目录整理模式，直接修改本地文件并处理相关附件。</p>

        <div class="alert alert-info mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <h3 class="font-bold">功能说明</h3>
            <div class="text-sm">
              <p>• <strong>智能匹配：</strong>为每个XML文件分别提取Publisher ID和DOI文件名</p>
              <p>• <strong>XML处理：</strong>直接修改XML文件内容和文件名（Open Access格式转换）</p>
              <p>• <strong>目录整理：</strong>支持平铺模式和JATS结构化包模式两种目录组织方式</p>
              <p>• <strong>附件处理：</strong>自动找到并处理与每个XML相关的附件文件</p>
              <p>• <strong>本地操作：</strong>直接修改本地文件，无需下载</p>
              <p>• <strong>支持格式：</strong>XML, PDF, MP4, JPG, JPEG, PNG, DOC, DOCX, TXT, ZIP</p>
            </div>
          </div>
        </div>

        <div class="alert alert-success">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 class="font-bold">使用说明</h3>
            <div class="text-sm">
              <p>1. 选择目录整理模式：平铺模式或结构化模式</p>
              <p>2. 点击"选择目录"按钮，选择包含XML文件和相关附件的文件夹</p>
              <p>3. 系统会自动扫描目录中的所有支持文件</p>
              <p>4. 点击"开始批量处理"，系统会：</p>
              <p class="ml-4">• 为每个XML文件提取其Publisher ID和新文件名</p>
              <p class="ml-4">• 处理XML文件的格式转换和重命名</p>
              <p class="ml-4">• 根据选择的模式整理附件文件</p>
              <p class="ml-4">• 结构化模式下自动创建统一的manifest.xml</p>
              <p class="ml-4">• Continuous Publication模式下额外创建issue目录和issue.xml</p>
              <p class="ml-4">• cover文件在Continuous Publication模式下会移动到issue目录</p>
              <p>5. 处理完成后，文件会按照选择的模式进行组织</p>
            </div>
          </div>
        </div>
      </div>

      {/* 文件选择区域 */}
      <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body">
          <h2 class="card-title">文件选择</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">文章类型</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={articleType()}
                onChange={(e) => setArticleType(e.target.value)}
              >
                <For each={articleTypes}>
                  {(type) => (
                    <option value={type.value}>{type.label}</option>
                  )}
                </For>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">目录整理模式</span>
              </label>
              <select
                class="select select-bordered w-full"
                value={organizationMode()}
                onChange={(e) => setOrganizationMode(e.target.value as 'flat' | 'structured')}
              >
                <For each={organizationModes}>
                  {(mode) => (
                    <option value={mode.value}>{mode.label}</option>
                  )}
                </For>
              </select>
              <label class="label">
                <span class="label-text-alt text-xs">
                  {organizationModes.find(m => m.value === organizationMode())?.description}
                </span>
              </label>
            </div>

            {/* 发布模式选择 - 仅在结构化模式下显示 */}
            {organizationMode() === 'structured' && (
              <div class="form-control">
                <label class="label">
                  <span class="label-text">发布模式</span>
                </label>
                <select
                  class="select select-bordered w-full"
                  value={publicationMode()}
                  onChange={(e) => setPublicationMode(e.target.value as 'pap' | 'continuous')}
                >
                  <For each={publicationModes}>
                    {(mode) => (
                      <option value={mode.value}>{mode.label}</option>
                    )}
                  </For>
                </select>
                <label class="label">
                  <span class="label-text-alt text-xs">
                    {publicationModes.find(m => m.value === publicationMode())?.description}
                  </span>
                </label>
              </div>
            )}
          </div>

          {/* 期刊元数据输入（仅在Continuous Publication模式下显示） */}
          {organizationMode() === 'structured' && publicationMode() === 'continuous' && (
            <div class="card bg-base-100 shadow-sm border mb-4">
              <div class="card-body p-4">
                <h3 class="card-title text-lg mb-3">期刊元数据设置</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">月份 *</span>
                    </label>
                    <input
                      type="text"
                      placeholder="例如: July-September"
                      class="input input-bordered"
                      value={issueMetadata().month}
                      onInput={(e) => setIssueMetadata(prev => ({...prev, month: e.target.value}))}
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">年份 *</span>
                    </label>
                    <input
                      type="text"
                      placeholder="例如: 2019"
                      class="input input-bordered"
                      value={issueMetadata().year}
                      onInput={(e) => setIssueMetadata(prev => ({...prev, year: e.target.value}))}
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">卷号 *</span>
                    </label>
                    <input
                      type="text"
                      placeholder="例如: 9"
                      class="input input-bordered"
                      value={issueMetadata().volume}
                      onInput={(e) => setIssueMetadata(prev => ({...prev, volume: e.target.value}))}
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">期号 *</span>
                    </label>
                    <input
                      type="text"
                      placeholder="例如: 3"
                      class="input input-bordered"
                      value={issueMetadata().issue}
                      onInput={(e) => setIssueMetadata(prev => ({...prev, issue: e.target.value}))}
                    />
                  </div>
                  <div class="form-control md:col-span-2">
                    <label class="label">
                      <span class="label-text">Issue ID *</span>
                    </label>
                    <input
                      type="text"
                      placeholder="例如: 20190900.0"
                      class="input input-bordered"
                      value={issueMetadata().issueId}
                      onInput={(e) => setIssueMetadata(prev => ({...prev, issueId: e.target.value}))}
                    />
                    <label class="label">
                      <span class="label-text-alt text-xs">
                        Issue ID将用作生成的XML文件名（如：20190900.0.xml）
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 模式说明 */}
          <div class="alert alert-warning mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
              <h3 class="font-bold">目录整理模式说明</h3>
              <div class="text-sm mt-2">
                <div class="mb-2">
                  <strong>平铺模式：</strong>所有文件保持在同一目录下，仅进行重命名操作
                  <div class="ml-4 text-xs text-gray-600">
                    适用于简单的文件重命名需求，保持原有目录结构
                  </div>
                </div>
                <div>
                  <strong>结构化模式：</strong>按JATS标准创建结构化目录
                  <div class="ml-4 text-xs text-gray-600">
                    为每个文章创建独立子目录，所有相关文件放在文章目录下<br/>
                    目录结构：根目录/ → manifest.xml, articles/article-id/<br/>
                    {publicationMode() === 'continuous' && '+ issue目录和issue.xml（Continuous Publication模式）'}<br/>
                    <div class="mt-1">
                      <strong>manifest.xml格式：</strong><br/>
                      • PAP模式：标准JATS格式，包含所有文章信息<br/>
                      • Continuous Publication模式：WK格式，包含issue信息和空的articles部分
                    </div>
                    自动创建：manifest.xml（{publicationMode() === 'continuous' ? 'WK格式期刊清单' : '所有文章清单'}）{publicationMode() === 'continuous' && '、issue.xml（期刊信息）'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {extractedInfo() && (
            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text">提取的信息</span>
              </label>
              <div class="bg-base-200 p-3 rounded">
                <p class="text-sm"><strong>Publisher ID:</strong> {extractedInfo()!.publisherId}</p>
                <p class="text-sm"><strong>新文件名:</strong> {extractedInfo()!.fileName}</p>
              </div>
            </div>
          )}

          <div class="form-control">
            <button
              type="button"
              class="btn btn-primary w-full"
              onClick={handleDirectorySelect}
              disabled={isProcessing()}
            >
              {isProcessing() && !rootDirectoryHandle() ? '选择中...' : '选择目录（包含XML及其他文件）'}
            </button>
            {rootDirectoryHandle() && (
              <p class="text-sm text-gray-500 mt-2">
                已选择目录：{rootDirectoryHandle()!.name}
              </p>
            )}
          </div>

          {selectedFiles().length > 0 && (
            <div class="mt-4">
              <p class="text-sm text-gray-600 mb-2">
                已选择 {selectedFiles().length} 个文件
                ({selectedFiles().filter(f => f.name.toLowerCase().endsWith('.xml')).length} 个XML文件,
                {selectedFiles().filter(f => !f.name.toLowerCase().endsWith('.xml')).length} 个其他文件)
              </p>
              <div class="max-h-32 overflow-y-auto">
                <For each={selectedFiles()}>
                  {(file) => (
                    <div class="badge badge-outline mr-2 mb-2">{file.name}</div>
                  )}
                </For>
              </div>
            </div>
          )}

          <div class="card-actions justify-end mt-4">
            <button
              type="button"
              class="btn btn-primary"
              onClick={handleBatchProcess}
              disabled={!canStartProcessing()}
            >
              {isProcessing() ? (
                <>
                  <span class="loading loading-spinner loading-sm"></span>
                  处理中...
                </>
              ) : (
                '开始批量处理'
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 进度显示区域 */}
      {isProcessing() && (
        <div class="card bg-base-100 shadow-xl mb-6">
          <div class="card-body">
            <h2 class="card-title">处理进度</h2>

            <div class="space-y-4">
              {/* 当前处理文件 */}
              <div class="text-sm text-gray-600">
                {currentProcessingFile()}
              </div>

              {/* 进度条 */}
              <div class="w-full">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>已处理: {processedCount()} / {totalCount()} 个文件</span>
                  <span>{progressPercentage()}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage()}%` }}
                  ></div>
                </div>
              </div>

              {/* 处理状态 */}
              <div class="flex items-center gap-2">
                <span class="loading loading-spinner loading-sm"></span>
                <span class="text-sm">正在处理文件，请稍候...</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 处理结果区域 */}
      {processResults().length > 0 && (
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <div class="flex justify-between items-center mb-4">
              <h2 class="card-title">处理结果</h2>
              <div class="text-sm text-gray-600">
                成功处理: {processResults().filter(r => r.success).length} / {processResults().length} 个文件
              </div>
            </div>

            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>文件名</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>信息</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <For each={processResults()}>
                    {(result) => (
                      <tr>
                        <td class="font-mono text-sm">{result.fileName}</td>
                        <td>
                          <div class={`badge ${
                            result.type === 'xml' ? 'badge-primary' :
                            result.type === 'organize' ? 'badge-accent' : 'badge-secondary'
                          }`}>
                            {result.type === 'xml' ? 'XML处理' :
                             result.type === 'organize' ? '文件整理' : '文件重命名'}
                          </div>
                        </td>
                        <td>
                          {result.success ? (
                            <div class="badge badge-success">成功</div>
                          ) : (
                            <div class="badge badge-error">失败</div>
                          )}
                        </td>
                        <td class="text-sm">
                          {result.success ? (
                            result.type === 'xml' ?
                              `${result.originalSize} → ${result.modifiedSize} bytes` :
                              result.newPath ?
                                `移动到: ${result.newPath}` :
                                `新文件名: ${result.newFileName}`
                          ) : (
                            <span class="text-error">{result.error}</span>
                          )}
                        </td>
                        <td>
                          {result.success ? (
                            <span class="text-success text-sm">✓ 已完成</span>
                          ) : (
                            <span class="text-error text-sm">✗ 失败</span>
                          )}
                        </td>
                      </tr>
                    )}
                  </For>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default XmlBatchModifier;